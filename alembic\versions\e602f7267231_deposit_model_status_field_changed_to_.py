"""deposit model status field changed to add more enum values

Revision ID: e602f7267231
Revises: c3452a9427ef
Create Date: 2025-05-29 12:19:50.320040

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e602f7267231'
down_revision: Union[str, None] = 'c3452a9427ef'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('deposits', 'status',
               existing_type=mysql.ENUM('PENDING', 'APPROVED', 'REJECTED', 'WITHDRAWN'),
               type_=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'CREDITED_TO_WALLET', name='depositstatus'),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('deposits', 'status',
               existing_type=sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'CREDITED_TO_WALLET', name='depositstatus'),
               type_=mysql.ENUM('PENDING', 'APPROVED', 'REJECTED', 'WITHDRAWN'),
               existing_nullable=True)
    # ### end Alembic commands ###
