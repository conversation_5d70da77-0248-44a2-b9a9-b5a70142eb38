from datetime import datetime
from typing import Optional, List
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text, Enum as sqlEnum, Date
from enum import Enum
from app.models.deposits import Deposit




class StatusEnum(str, Enum):
    pending = "pending"
    verified = "verified"
    rejected = "rejected"

# USERS Table
class User(BaseModel):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    dob = Column(Date)
    nationality = Column(String(100), nullable=False)
    country_of_residence = Column(String(100), nullable=False)
    preferred_currency = Column(Integer, ForeignKey("currency_conversions.id"))
    address = Column(Text, nullable=False)
    country_code = Column(String(10), nullable=False)
    phone = Column(String(15), nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password = Column(Text, nullable=False)
    referral_code = Column(String(500), unique=True, index=True, nullable=False)
    referrer_id = Column(Integer, ForeignKey("users.id"))
    national_id = Column(Text, nullable=False)
    passport = Column(Text, nullable=False)
    rank_id = Column(Integer, ForeignKey("ranks.id")) #platinum, gold, silver
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    is_kyc_verified = Column(Boolean, default=False)
    kyc_status = Column(sqlEnum(StatusEnum), default=StatusEnum.pending) #pending, verified, rejected
    kyc_status_reason = Column(Text)
    profile_picture = Column(Text)
    direct_user_count = Column(Integer, default=0)
    leg_user_count = Column(Integer, default=0)
    leg_deposit_total = Column(Float, default=0.0)
    
    # Relationships
    deposits = relationship("Deposit", back_populates="user", foreign_keys=[Deposit.user_id])
    wallet = relationship("Wallet", uselist=False, back_populates="user")
    profit_logs = relationship("ProfitLog", back_populates="user")
    withdrawals = relationship("Withdrawal", back_populates="user")
    # referral_commissions_given = relationship("ReferralCommission", foreign_keys="ReferralCommission.referrer_id", back_populates="referrer")
    # referral_commissions_received = relationship("ReferralCommission", foreign_keys="ReferralCommission.referred_user_id", back_populates="referred_user")
    transactions = relationship("TransactionLedger", back_populates="user")
    referral_commissions_given = relationship("ReferralCommission",foreign_keys="ReferralCommission.depositing_user_id",back_populates="original_depositor_info")
    referral_commissions_received = relationship("ReferralCommission",foreign_keys="ReferralCommission.recipient_user_id",back_populates="recipient")
    rank = relationship("Rank", back_populates="user")
    referrer = relationship("User", remote_side=[id])
    currency = relationship("CurrencyConversion", back_populates="users")


    @property
    def rank_name(self):
        return self.rank.name if self.rank else None
    
    @property
    def referrer_name(self):
        return self.referrer.name if self.referrer else None

    @property
    def preferred_currency_name(self):
        return self.currency.from_currency if self.currency else None
    
    @property
    def preferred_currency_code(self):
        return self.currency.code if self.currency else None
    
    @property
    def current_total_referrals(self):
        return self.wallet.total_referral_bonus if self.wallet else 0.0


class EmailOTP(BaseModel):
    __tablename__ = "email_otp"

    email = Column(String(300), primary_key=True, index=True)
    otp = Column(Integer, nullable=False)
    expires_at = Column(DateTime, nullable=False)
