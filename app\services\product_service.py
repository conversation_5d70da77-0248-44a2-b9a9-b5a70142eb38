from sqlalchemy.orm import Session
from app.models.products import Product
from app.schemas.products import ProductCreate, ProductUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_products(db: Session, params: PaginationParams) -> Page[Product]:
    query = db.query(Product).order_by(Product.id.desc())
    return paginate(query, params)

def get_product(db: Session, product_id: int) -> Optional[Product]:
    return db.query(Product).filter(Product.id == product_id).first()

def create_product(db: Session, product: ProductCreate) -> Product:
    db_product = Product(**product.dict())
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product

def update_product(db: Session, product_id: int, update_data: dict) -> Optional[Product]:
    db_product = get_product(db, product_id)
    if db_product:
        for key, value in update_data.items():
            setattr(db_product, key, value)
        db.commit()
        db.refresh(db_product)
    return db_product

def delete_product(db: Session, product_id: int) -> bool:
    db_product = get_product(db, product_id)
    if db_product:
        db.delete(db_product)
        db.commit()
        return True
    return False