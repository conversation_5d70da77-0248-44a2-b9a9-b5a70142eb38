from datetime import datetime
from typing import Optional, Text
from pydantic import BaseModel as PydanticBaseModel
from app.schemas.base import CustomBaseModel

class AdminWalletBase(CustomBaseModel):
    wallet_address: str
    qr_code: Text
    network: str
    is_active: bool = True


class AdminWalletCreate(AdminWalletBase):
    pass


class AdminWalletUpdate(PydanticBaseModel):
    wallet_address: Optional[str] = None
    qr_code: Optional[str] = None
    network: Optional[str] = None
    is_active: Optional[bool] = None


class AdminWalletOut(AdminWalletBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
