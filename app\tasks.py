# tasks/reward_tasks.py
from app.models import User
from app.models.referral_reward import UserRewardProgress
from app.models.referral_reward import get_db_session
from datetime import datetime
from app.celery_app import celery_app
from app.models.user_ranks import Rank

@celery_app.task
def process_deposit_reward_updates(depositor_id: int, deposit_amount: float):
    print(f"Task started for user {depositor_id} with amount {deposit_amount}")
    db = get_db_session()

    current = db.query(User).get(depositor_id)

    while current and current.referrer_id:
        referrer = db.query(User).get(current.referrer_id)

        referrer.leg_deposit_total = (referrer.leg_deposit_total or 0.0) + deposit_amount
        db.add(referrer)
        check_and_award_rewards(referrer, db)

        current = referrer

    db.commit()


def check_and_award_rewards(user: User, db):
    print(f"\n🔍 Checking rewards for user {user.id}")
    print(f"   ➤ Direct users: {user.direct_user_count}")
    print(f"   ➤ Leg users: {user.leg_user_count}")
    print(f"   ➤ Leg deposits: {user.leg_deposit_total}")
    print(f"   ➤ Current rank: {user.rank.name if user.rank else 'None'} (ID: {user.rank_id})")

    rewards = db.query(Rank).order_by(Rank.id).all()
    wallet = user.wallet

    qualified_index = -1  # Track the index of highest rank for which user qualifies

    for idx, reward in enumerate(rewards):
        print(f"\n➡️ Evaluating rank: {reward.name} (ID: {reward.id})")
        print(f"   - Required direct users: {reward.required_direct_users}")
        print(f"   - Required leg users: {reward.required_total_users}")
        print(f"   - Required deposits: {reward.required_deposit}")

        qualifies = (
            (user.direct_user_count or 0) >= (reward.required_direct_users or 0) and
            (user.leg_user_count or 0) >= (reward.required_total_users or 0) and
            (user.leg_deposit_total or 0) >= (reward.required_deposit or 0)
        )

        print(f"   ✅ Qualifies? {'Yes' if qualifies else 'No'}")

        if qualifies:
            qualified_index = idx
            already_awarded = db.query(UserRewardProgress).filter_by(
                user_id=user.id,
                rank_id=reward.id
            ).first()

            if not already_awarded:
                print(f"🎁 Granting reward for rank {reward.name} (bonus: {reward.bonus_amount})")
                db.add(UserRewardProgress(
                    user_id=user.id,
                    rank_id=reward.id,
                    is_achieved=True,
                    achieved_on=datetime.utcnow()
                ))

                if wallet:
                    wallet.total_referral_bonus += reward.bonus_amount
                    wallet.current_referral_bonus += reward.bonus_amount
                    db.add(wallet)
            else:
                print(f"⚠️ Already rewarded for {reward.name}")
        else:
            print(f"🛑 Requirements not met for {reward.name}")

    if qualified_index + 1 < len(rewards):
        next_rank = rewards[qualified_index + 1]
        print(f"\n🚀 Promoting to next rank: {next_rank.name} (ID: {next_rank.id})")
        user.rank_id = next_rank.id
        db.add(user)
    else:
        if qualified_index == len(rewards) - 1:
            print(f"\n🏁 Already at highest rank: {rewards[qualified_index].name}")
        elif qualified_index == -1:
            print("\n❌ User doesn't meet any rank criteria")
        else:
            print(f"\n✅ User retains current rank: {rewards[qualified_index].name}")

    db.commit()
    print(f"✅ Final rank_id saved: {user.rank_id}\n")



    
    

# utils/user_signup.py
from app.models import User
from app.models.referral_reward import get_db_session

@celery_app.task
def handle_user_signup(new_user_id: int):
    print(f"handle user signup Task started for user {new_user_id}")
    db = get_db_session()
    user = db.query(User).get(new_user_id)

    if user.referrer_id:
        referrer = db.query(User).get(user.referrer_id)
        referrer.direct_user_count = (referrer.direct_user_count or 0) + 1
        db.add(referrer)

    current = user
    while current.referrer_id:
        current = db.query(User).get(current.referrer_id)
        current.leg_user_count = (current.leg_user_count or 0) + 1
        db.add(current)

    db.commit()