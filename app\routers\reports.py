from fastapi import APIRouter, Request, Depends, Response, Query
from sqlalchemy.orm import Session
from datetime import datetime
import io
from fastapi.templating import Jinja2Templates
from app.config.db import get_db
from app.utils.pdf import render_pdf_report
from app.models.users import User
from app.models.deposits import Deposit
from app.models.withdrawals import Withdrawal
from app.utils.auth_utility import get_current_user  # Adjust to your setup
from fastapi.responses import HTMLResponse


router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/transaction-report", response_class=HTMLResponse)
def generate_transaction_report(
    request: Request,
    type: str = Query(..., regex="^(deposit|withdrawal)$"),
    db: Session = Depends(get_db),
    # current_user: User = Depends(get_current_user)
):
    current_user = db.query(User).filter(User.email == "<EMAIL>").first()
    if type == "deposit":
        transactions = (
            db.query(Deposit)
            .filter(Deposit.user_id == current_user.id)
            .order_by(Deposit.id.desc())
            .all()
        )
        template_name = "deposit_report.html"
        print(f"this is status {transactions[0].status.value}")
    else:
        transactions = (
            db.query(Withdrawal)
            .filter(Withdrawal.user_id == current_user.id)
            .order_by(Withdrawal.id.desc())
            .all()
        )
        template_name = "withdrawal_report.html"

    context = {
        "user": current_user,
        "transactions": transactions,
        "report_title": f"{type.capitalize()} Report",
        "issued_date": datetime.now().strftime("%d-%m-%Y %H:%M"),
        "request": request  # if you're using {{ request.url }} in template
    }

    pdf_content = render_pdf_report(template_name, context)

    return Response(
        content=pdf_content,
        media_type="application/pdf",
        headers={
            "Content-Disposition": f"attachment; filename={type}_report.pdf"
        }
    )