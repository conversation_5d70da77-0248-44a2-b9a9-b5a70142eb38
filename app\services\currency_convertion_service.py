from sqlalchemy.orm import Session
from app.models.currency_convertion import CurrencyConversion
from app.schemas.currency_convertion import CurrencyConversionCreate, CurrencyConversionUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_currency_conversions(db: Session, params: PaginationParams) -> Page[CurrencyConversion]:
    query = db.query(CurrencyConversion).order_by(CurrencyConversion.id.desc())
    return paginate(query, params)

def get_currency_conversion(db: Session, CurrencyConversion_id: int) -> Optional[CurrencyConversion]:
    return db.query(CurrencyConversion).filter(CurrencyConversion.id == CurrencyConversion_id).first()

def create_currency_conversion(db: Session, CurrencyConversionData: CurrencyConversionCreate) -> CurrencyConversion:
    db_CurrencyConversion = CurrencyConversion(**CurrencyConversionData.dict())
    db.add(db_CurrencyConversion)
    db.commit()
    db.refresh(db_CurrencyConversion)
    return db_CurrencyConversion

def update_currency_conversion(db: Session, CurrencyConversion_id: int, CurrencyConversion: CurrencyConversionUpdate) -> Optional[CurrencyConversion]:
    db_CurrencyConversion = get_currency_conversion(db, CurrencyConversion_id)
    if db_CurrencyConversion:
        update_data = CurrencyConversion.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_CurrencyConversion, key, value)
        db.commit()
        db.refresh(db_CurrencyConversion)
    return db_CurrencyConversion

def delete_currency_conversion(db: Session, CurrencyConversion_id: int) -> bool:
    db_CurrencyConversion = get_currency_conversion(db, CurrencyConversion_id)
    if db_CurrencyConversion:
        db.delete(db_CurrencyConversion)
        db.commit()
        return True
    return False