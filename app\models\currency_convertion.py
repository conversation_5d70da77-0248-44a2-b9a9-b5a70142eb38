from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey




class CurrencyConversion(BaseModel):
    __tablename__ = "currency_conversions"
    
    id = Column(Integer, primary_key=True)
    from_currency = Column(String(50), nullable=False)
    code = Column(String(10), nullable=False)
    conversion_rate = Column(Float, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    users = relationship("User", back_populates="currency")