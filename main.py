from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from app.models.privacy_and_policy import PrivacyAndPolicy
from app.models.terms_and_conditions import TermsAndConditions
from app.models.admin_wallet import AdminWallet
# from app.tasks import send_welcome_email
# from .app.router import album_router
from app.routers import (admin_profit_router,
                         admin_wallet_router,
                         currency_convertion_router,
                         deposit_router,
                         deposite_slab_router,
                         privacy_and_policy_router,
                         product_router,
                         profit_log_router,
                         referral_level_percentage,
                         terms_and_condition_router,
                         transaction_log_router,
                         users_router,
                         wallet_router,
                         withdrawal_router,
                         dashboard,
                         ranks,
                         reports)





app=FastAPI()

app.mount("/static", StaticFiles(directory="static"), name="static")




# CORS Configuration - Allow all for debugging
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



app.include_router(admin_profit_router.router)
app.include_router(admin_wallet_router.router)
app.include_router(currency_convertion_router.router)
app.include_router(deposit_router.router)
app.include_router(deposite_slab_router.router)
app.include_router(privacy_and_policy_router.router)
app.include_router(product_router.router)
app.include_router(profit_log_router.router)
app.include_router(referral_level_percentage.router)
app.include_router(terms_and_condition_router.router)
app.include_router(transaction_log_router.router)
app.include_router(users_router.router)
app.include_router(wallet_router.router)
app.include_router(withdrawal_router.router)
app.include_router(dashboard.router)
app.include_router(ranks.router)
app.include_router(reports.router)

# Simple test endpoint for CORS debugging
@app.get("/test-cors")
def test_cors():
    return {"message": "CORS is working!", "status": "success"}

@app.post("/test-cors-post")
def test_cors_post(data: dict):
    return {"message": "CORS POST is working!", "received_data": data}



# this is to seed initial referral levels into db when the application starts.


from fastapi import FastAPI, Depends
from sqlalchemy.orm import Session

# Adjust imports based on your project structure
from app.config.db import SessionLocal # Or however you get your DB Session factory
from app.utils.initial_seeding_data import create_initial_referral_levels, create_initial_ranks # The function we just defined
from app.config.db import Base # If you need to create tables (usually for dev)
from app.config.db import engine # If you need to create tables (usually for dev)


# This is just an example, you might have a more sophisticated way to create tables.
# In production, migrations (like Alembic) are preferred over create_all.
def create_tables():
    print("Creating database tables if they don't exist...")
    Base.metadata.create_all(bind=engine)


@app.on_event("startup")
async def on_startup():
    print("Application startup event triggered.")
    # create_tables() # Call this only if you want FastAPI to create tables (e.g., for local dev with SQLite)

    db = SessionLocal() # Create a new session instance for the startup event
    try:
        print("--- Running initial data setup ---")
        print("Warning: Database configuration and migrations of the model or tables should be done before starting the application since initial referral level model data seeding is in process")
        create_initial_referral_levels(db=db)
        create_initial_ranks(db=db)
        # create_initial_referral_rewards(db=db)
        
        # Privacy and Policy
        existing_privacy = db.query(PrivacyAndPolicy).first()
        if not existing_privacy:
            privacy_and_policy = PrivacyAndPolicy(
                title="Privacy Policy",
                description="This is our privacy policy...",
                is_active=True
            )
            db.add(privacy_and_policy)
            db.commit()
            db.refresh(privacy_and_policy)
        
        # Terms and Conditions
        existing_terms = db.query(TermsAndConditions).first()
        if not existing_terms:
            terms_and_conditions = TermsAndConditions(
                title="Terms and Conditions",
                description="These are our terms and conditions...",
                is_active=True
            )
            db.add(terms_and_conditions)
            db.commit()
            db.refresh(terms_and_conditions)
        
        # Admin Wallet
        existing_wallet = db.query(AdminWallet).filter_by(wallet_address="0x1234567890abcdef").first()
        if not existing_wallet:
            admin_wallet = AdminWallet(
                wallet_address="0x1234567890abcdef",
                qr_code="static/images/QR.jpg",
                network="ETH",
                is_active=True
            )
            db.add(admin_wallet)
            db.commit()
            db.refresh(admin_wallet)
        
        print("--- Initial data setup finished ---")
    finally:
        db.close() # Always close the session

# ... your other FastAPI routes and configurations ...

# Example endpoint (not related to seeding, just for context)
@app.get("/")
async def read_root():
    return {"message": "Welcome to the application!"}





#Celery

# @app.post("/register/")
# def register_user(email: str):
#     # Do some synchronous stuff like saving user
#     send_welcome_email.delay(email)
#     return {"message": "User registered, email will be sent soon."}