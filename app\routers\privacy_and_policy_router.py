from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.privacy_and_policy import PrivacyAndPolicyCreate, PrivacyAndPolicyUpdate, PrivacyAndPolicyOut
from app.services.privacy_and_policy_service import get_privacy_and_policys, get_privacy_and_policy, create_privacy_and_policy, update_privacy_and_policy, delete_privacy_and_policy
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/privacy_and_policys",
    tags=["privacy_and_policys"]
)

@router.get("/", response_model=Page[PrivacyAndPolicyOut])
def read_privacy_and_policys(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_privacy_and_policys(db, params=params)

@router.get("/{privacy_and_policy_id}", response_model=PrivacyAndPolicyOut)
def read_privacy_and_policy(privacy_and_policy_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    privacy_and_policy = get_privacy_and_policy(db, privacy_and_policy_id=privacy_and_policy_id)
    if privacy_and_policy is None:
        raise HTTPException(status_code=404, detail="privacy_and_policy not found")
    return privacy_and_policy

@router.post("/", response_model=PrivacyAndPolicyOut, status_code=status.HTTP_201_CREATED)
def create_new_privacy_and_policy(privacy_and_policy: PrivacyAndPolicyCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_privacy_and_policy(db=db, PrivacyAndPolicyData=privacy_and_policy)

@router.put("/{privacy_and_policy_id}", response_model=PrivacyAndPolicyOut)
def update_existing_privacy_and_policy(privacy_and_policy_id: int, privacy_and_policy: PrivacyAndPolicyUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_privacy_and_policy = update_privacy_and_policy(db=db, PrivacyAndPolicy_id=privacy_and_policy_id, PrivacyAndPolicy=privacy_and_policy)
    if updated_privacy_and_policy is None:
        raise HTTPException(status_code=404, detail="privacy_and_policy not found")
    return updated_privacy_and_policy

@router.delete("/{privacy_and_policy_id}", status_code=status.HTTP_200_OK)
def delete_existing_privacy_and_policy(privacy_and_policy_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_privacy_and_policy(db=db, privacy_and_policy_id=privacy_and_policy_id)
    if not success:
        raise HTTPException(status_code=404, detail="privacy_and_policy not found")
    return {"detail": "privacy_and_policy deleted successfully"}