"""create models related to referral reward system

Revision ID: 0b90d02ba3fb
Revises: b04f5c21b9a6
Create Date: 2025-06-10 16:08:18.430223

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0b90d02ba3fb'
down_revision: Union[str, None] = 'b04f5c21b9a6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('milestone_rewards',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('required_direct_users', sa.Integer(), nullable=True),
    sa.Column('required_total_users', sa.Integer(), nullable=True),
    sa.Column('required_deposit', sa.Float(), nullable=True),
    sa.Column('reward_description', sa.String(length=500), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_reward_progress',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('reward_id', sa.Integer(), nullable=True),
    sa.Column('is_achieved', sa.Boolean(), nullable=True),
    sa.Column('achieved_on', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['reward_id'], ['milestone_rewards.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_reward_progress')
    op.drop_table('milestone_rewards')
    # ### end Alembic commands ###
