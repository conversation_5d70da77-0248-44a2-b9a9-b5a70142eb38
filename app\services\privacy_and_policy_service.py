from sqlalchemy.orm import Session
from app.models.privacy_and_policy import PrivacyAndPolicy
from app.schemas.privacy_and_policy import PrivacyAndPolicyCreate, PrivacyAndPolicyUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_privacy_and_policys(db: Session, params: PaginationParams) -> Page[PrivacyAndPolicy]:
    query = db.query(PrivacyAndPolicy)
    return paginate(query, params)

def get_privacy_and_policy(db: Session, PrivacyAndPolicy_id: int) -> Optional[PrivacyAndPolicy]:
    return db.query(PrivacyAndPolicy).filter(PrivacyAndPolicy.id == PrivacyAndPolicy_id).first()

def create_privacy_and_policy(db: Session, PrivacyAndPolicyData: PrivacyAndPolicyCreate) -> PrivacyAndPolicy:
    db_PrivacyAndPolicy = PrivacyAndPolicy(**PrivacyAndPolicyData.dict())
    db.add(db_PrivacyAndPolicy)
    db.commit()
    db.refresh(db_PrivacyAndPolicy)
    return db_PrivacyAndPolicy

def update_privacy_and_policy(db: Session, PrivacyAndPolicy_id: int, PrivacyAndPolicy: PrivacyAndPolicyUpdate) -> Optional[PrivacyAndPolicy]:
    db_PrivacyAndPolicy = get_privacy_and_policy(db, PrivacyAndPolicy_id)
    if db_PrivacyAndPolicy:
        update_data = PrivacyAndPolicy.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_PrivacyAndPolicy, key, value)
        db.commit()
        db.refresh(db_PrivacyAndPolicy)
    return db_PrivacyAndPolicy

def delete_privacy_and_policy(db: Session, PrivacyAndPolicy_id: int) -> bool:
    db_PrivacyAndPolicy = get_privacy_and_policy(db, PrivacyAndPolicy_id)
    if db_PrivacyAndPolicy:
        db.delete(db_PrivacyAndPolicy)
        db.commit()
        return True
    return False