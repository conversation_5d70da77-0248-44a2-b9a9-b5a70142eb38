from app.models.base import BaseModel
from sqlalchemy import Column, Integer, String, Numeric

class ReferralLevelPercentage(BaseModel):
    __tablename__ = "referral_level_percentages"
    level = Column(Integer, primary_key=True)
    percentage = Column(Numeric(4, 2), nullable=False)
    description = Column(String(255), nullable=True)

    def __repr__(self):
        return f"<ReferralLevelPercentage level={self.level} percentage={self.percentage}>"