# utils/save_image.py

import os
import shutil
from fastapi import UploadFile

UPLOAD_DIR = "static/images"
os.makedirs(UPLOAD_DIR, exist_ok=True)

def save_image(file: UploadFile) -> str:
    """
    Save uploaded image file to disk and return relative path.

    Args:
        file (UploadFile): The image file uploaded from the client.

    Returns:
        str: The relative path where the file was saved.
    """
    file_path = os.path.join(UPLOAD_DIR, file.filename)

    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)

    return f"{UPLOAD_DIR}/{file.filename}"
