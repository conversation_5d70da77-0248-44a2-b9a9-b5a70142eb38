<!-- templates/deposit_report.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report_title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .report-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .report-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        .report-content {
            padding: 40px;
        }

        .user-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
            border-left: 5px solid #667eea;
        }

        .user-info h3 {
            color: #2d3748;
            font-size: 1.3rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 0.85rem;
            color: #718096;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 1rem;
            color: #2d3748;
            font-weight: 500;
        }

        .table-section {
            margin-top: 20px;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .table-title {
            font-size: 1.5rem;
            color: #2d3748;
            font-weight: 600;
        }

        .transaction-count {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        th {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 3px solid #667eea;
        }

        td {
            padding: 16px 15px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        tr:hover {
            background-color: #f7fafc;
            transition: background-color 0.2s ease;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-approved {
            background-color: #c6f6d5;
            color: #22543d;
        }

        .status-pending {
            background-color: #fef5e7;
            color: #c05621;
        }

        .status-rejected {
            background-color: #fed7d7;
            color: #c53030;
        }

        .amount-cell {
            font-weight: 600;
            color: #38a169;
            font-family: 'Courier New', monospace;
        }

        .date-cell {
            color: #4a5568;
            font-size: 0.85rem;
        }

        .transaction-id {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #718096;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .report-container {
                box-shadow: none;
                border: none;
                border-radius: 0;
            }

            .report-header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        @media (max-width: 768px) {
            .report-container {
                margin: 10px;
                border-radius: 12px;
            }

            .report-header {
                padding: 30px 20px;
            }

            .report-title {
                font-size: 2rem;
            }

            .report-content {
                padding: 20px;
            }

            .user-info {
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .table-container {
                overflow-x: auto;
            }

            table {
                min-width: 600px;
            }

            th, td {
                padding: 12px 8px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">{{ report_title }}</h1>
            <p class="report-subtitle">Financial Transaction Statement</p>
        </div>

        <div class="report-content">
            <div class="user-info">
                <h3>Account Information</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Account Holder</span>
                        <span class="info-value">{{ user.name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email Address</span>
                        <span class="info-value">{{ user.email }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Report Generated</span>
                        <span class="info-value">{{ issued_date }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total Transactions</span>
                        <span class="info-value">{{ transactions|length }}</span>
                    </div>
                </div>
            </div>

            <div class="table-section">
                <div class="table-header">
                    <h3 class="table-title">Transaction Details</h3>
                    <span class="transaction-count">{{ transactions|length }} Records</span>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Transaction ID</th>
                                <th>Admin Wallet ID</th>
                                <th>Approved Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for txn in transactions %}
                            <tr>
                                <td class="date-cell">
                                    {% if txn.created_at %}
                                        {{ txn.created_at.strftime('%d %b %Y') }}<br>
                                        {{ txn.created_at.strftime('%H:%M') }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td class="amount-cell">
                                    ${{ "%.2f"|format(txn.amount) }}
                                </td>
                                <td>
                                    <span class="status-badge status-{{ txn.status.lower() }}">
                                        {{ txn.status.value }}
                                    </span>
                                </td>
                                <td class="transaction-id">{{ txn.transaction_id }}</td>
                                <td class="transaction-id">{{ txn.admin_wallet_id or 'N/A' }}</td>
                                <td class="date-cell">
                                    {% if txn.approved_at %}
                                        {{ txn.approved_at.strftime('%d %b %Y') }}<br>
                                        {{ txn.approved_at.strftime('%H:%M') }}
                                    {% else %}
                                        Pending
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
