<!-- templates/transaction_report.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ report_title }}</title>
    <style>
        body {
            font-family: "Helvetica", "Arial", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7fb;
        }
        .statement-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header-title {
            font-size: 36px;
            font-weight: bold;
            color: #1DABD9;
        }
        .meta {
            margin-top: 20px;
            font-size: 14px;
        }
        .table-container {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #e0e0e0;
        }
        th {
            background-color: #1DABD9;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="statement-container">
        <div class="header">
            <div class="header-title">{{ report_title }}</div>
        </div>
        <div class="meta">
            <p><strong>Name:</strong> {{ user.name }}</p>
            <p><strong>Email:</strong> {{ user.email }}</p>
            <p><strong>Issued Date:</strong> {{ issued_date }}</p>
            <p><strong>Total Transactions:</strong> {{ transactions|length }}</p>
        </div>
        <div class="table-container">
            <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Transaction ID</th>
                    <th>Admin Wallet ID</th>
                    <th>Approved Date</th>
                  </tr>
                </thead>
                <tbody>
                  {% for txn in transactions %}
                  <tr>
                    <td>{{ txn.created_at.strftime('%d-%m-%Y %H:%M') if txn.created_at else '' }}</td>
                    <td>{{ txn.amount }}</td>
                    <td>{{ txn.status }}</td>
                    <td>{{ txn.transaction_id }}</td>
                    <td>{{ txn.admin_wallet_id }}</td>
                    <td>{{ txn.approved_at.strftime('%d-%m-%Y %H:%M') if txn.approved_at else '' }}</td>
                  </tr>
                  {% endfor %}
                </tbody>

            </table>
        </div>
    </div>
</body>
</html>
