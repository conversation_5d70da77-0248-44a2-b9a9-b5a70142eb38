"""models status reason update, image added in slab, emailotp model added

Revision ID: 8b539fba5981
Revises: 1da0352f0a11
Create Date: 2025-06-02 12:02:29.245572

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8b539fba5981'
down_revision: Union[str, None] = '1da0352f0a11'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_otp',
    sa.Column('email', sa.String(length=300), nullable=False),
    sa.Column('otp', sa.Integer(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('email')
    )
    op.create_index(op.f('ix_email_otp_email'), 'email_otp', ['email'], unique=False)
    op.add_column('deposit_slabs', sa.Column('image', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('kyc_status_reason', sa.Text(), nullable=True))
    op.add_column('withdrawals', sa.Column('status_update_reason', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('withdrawals', 'status_update_reason')
    op.drop_column('users', 'kyc_status_reason')
    op.drop_column('deposit_slabs', 'image')
    op.drop_index(op.f('ix_email_otp_email'), table_name='email_otp')
    op.drop_table('email_otp')
    # ### end Alembic commands ###
