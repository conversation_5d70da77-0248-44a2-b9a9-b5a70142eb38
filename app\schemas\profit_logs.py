from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum
from app.schemas.base import CustomBaseModel



class ProfitLogBase(CustomBaseModel):
    # deposit_id: int
    user_name: str
    profit_amount: float
    profit_percent: float

class ProfitLogCreate(ProfitLogBase):
    pass

class ProfitLogOut(ProfitLogBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True