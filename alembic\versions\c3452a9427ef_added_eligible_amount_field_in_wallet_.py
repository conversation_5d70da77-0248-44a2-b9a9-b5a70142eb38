"""added eligible amount field in wallet model

Revision ID: c3452a9427ef
Revises: 3b246065b850
Create Date: 2025-05-28 19:12:45.656340

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c3452a9427ef'
down_revision: Union[str, None] = '3b246065b850'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('wallets', sa.Column('eligible_amount', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('wallets', 'eligible_amount')
    # ### end Alembic commands ###
