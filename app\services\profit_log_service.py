from sqlalchemy.orm import Session
from app.models.profit_logs import ProfitLog
from app.schemas.profit_logs import ProfitLogCreate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from datetime import datetime, timedelta
from sqlalchemy.orm import joinedload

def get_profit_logs(db: Session, params: PaginationParams) -> Page[ProfitLog]:
    query = db.query(ProfitLog).order_by(ProfitLog.id.desc())
    return paginate(query, params)

def get_user_profit_log(db: Session, user_id: int, filter: str) -> List[ProfitLog]:
    now = datetime.now()
    query = db.query(ProfitLog).options(joinedload(ProfitLog.user)).filter(ProfitLog.user_id == user_id).order_by(ProfitLog.id.desc())


    if filter == "week":
        start_date = now - timedelta(days=now.weekday())  # start of the week (Monday)
        query = query.filter(ProfitLog.created_at >= start_date)
    elif filter == "month":
        start_date = now.replace(day=1)
        query = query.filter(ProfitLog.created_at >= start_date)
    elif filter == "year":
        start_date = now.replace(month=1, day=1)
        query = query.filter(ProfitLog.created_at >= start_date)

    return query.all()

def create_profit_log(db: Session, ProfitLog: ProfitLogCreate) -> ProfitLog:
    db_profit_log = ProfitLog(**ProfitLog.dict())
    db.add(db_profit_log)
    db.commit()
    db.refresh(db_profit_log)
    return db_profit_log

# def update_profit_log(db: Session, profit_log_id: int, ProfitLog: profit_logUpdate) -> Optional[ProfitLog]:
#     db_profit_log = get_profit_log(db, profit_log_id)
#     if db_profit_log:
#         update_data = ProfitLog.dict(exclude_unset=True)
#         for key, value in update_data.items():
#             setattr(db_profit_log, key, value)
#         db.commit()
#         db.refresh(db_profit_log)
#     return db_profit_log

def delete_profit_log(db: Session, profit_log_id: int) -> bool:
    db_profit_log = db.query(ProfitLog).filter(ProfitLog.id == profit_log_id).first()
    if db_profit_log:
        db.delete(db_profit_log)
        db.commit()
        return True
    return False