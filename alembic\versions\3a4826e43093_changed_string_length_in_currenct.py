"""changed string length in currenct

Revision ID: 3a4826e43093
Revises: 85bad136a8a4
Create Date: 2025-06-06 14:47:16.655171

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3a4826e43093'
down_revision: Union[str, None] = '85bad136a8a4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('currency_conversions', 'from_currency',
               existing_type=mysql.VARCHAR(length=10),
               type_=sa.String(length=50),
               existing_nullable=False)
    op.alter_column('products', 'description',
               existing_type=mysql.TEXT(),
               type_=mysql.LONGTEXT(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('products', 'description',
               existing_type=mysql.LONGTEXT(),
               type_=mysql.TEXT(),
               existing_nullable=False)
    op.alter_column('currency_conversions', 'from_currency',
               existing_type=sa.String(length=50),
               type_=mysql.VARCHAR(length=10),
               existing_nullable=False)
    # ### end Alembic commands ###
