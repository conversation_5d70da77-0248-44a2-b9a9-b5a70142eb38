from sqlalchemy.orm import declarative_base
from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, Boolean

Base = declarative_base()



class BaseModel(Base):
    __abstract__ = True
    
    is_deleted = Column(Boolean, default=False, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<{self.__class__.__name__} id={self.id}>"