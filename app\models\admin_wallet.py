from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text





class AdminWallet(BaseModel):
    __tablename__ = "admin_wallets"
    
    id = Column(Integer, primary_key=True)
    wallet_address = Column(String(100), nullable=False)
    qr_code = Column(Text, nullable=False)
    network = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    
    # deposits = relationship("Deposit", back_populates="admin_wallet")