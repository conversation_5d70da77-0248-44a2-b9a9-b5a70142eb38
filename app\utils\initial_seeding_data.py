# In a new file, e.g., app/initial_data.py or app/db/init_db.py
from sqlalchemy.orm import Session
from app.models.referral_percentage import ReferralLevelPercentage # Adjust import path
# Import your SessionLocal or however you create a DB session for scripts/startup
from app.config.db import SessionLocal # Example: if you have SessionLocal defined here
from app.models.user_ranks import Rank
# from app.models.referral_reward import MilestoneReward

# Or, if your main.py creates the engine and SessionLocal, you might pass SessionLocal
# or a dependency to get the DB session. For startup events, creating a local session is common.

REFERRAL_LEVELS_DATA = [
    {"level": 1, "percentage": 7.00, "description": "Direct referral bonus"},
    {"level": 2, "percentage": 5.00, "description": "Second-tier referral bonus"},
    {"level": 3, "percentage": 4.00, "description": "Third-tier referral bonus"},
    {"level": 4, "percentage": 2.50, "description": "Fourth-tier referral bonus"},
    {"level": 5, "percentage": 1.00, "description": "Fifth-tier referral bonus"},
]

RANKS_DATA = [
    {
        "name": "BRONZE", 
        "discription": "BRONZE - 6,000 reward\nDirect referrals: 10\nIndirect referrals: 15\nTotal: 25 users", 
        "threshold_amount": 50000, 
        "bonus_amount": 6000, 
        "tier_level": "1",
        "required_direct_users": 10,
        "required_total_users": 25,
        "required_deposit": 50000,
        "reward_description": "$6,000 reward\nDirect referrals: 10\nIndirect referrals: 15\nTotal: 25 users"
    },
    {
        "name": "SILVER", 
        "discription": "SILVER - 15,000 reward\nDirect referrals: 15\nIndirect referrals: 35\nTotal: 50 users", 
        "threshold_amount": 100000, 
        "bonus_amount": 15000, 
        "tier_level": "2",
        "required_direct_users": 15,
        "required_total_users": 50,
        "required_deposit": 100000,
        "reward_description": "$15,000 reward\nDirect referrals: 15\nIndirect referrals: 35\nTotal: 50 users"
    },
    {
        "name": "GOLD", 
        "discription": "GOLD - 35,000 reward\nTotal: 50+ users (direct or indirect)", 
        "threshold_amount": 200000, 
        "bonus_amount": 35000, 
        "tier_level": "3",
        "required_direct_users": None,
        "required_total_users": 50,
        "required_deposit": 200000,
        "reward_description": "$35,000 reward\nTotal: 50+ users (direct or indirect)"
    },
    {
        "name": "PLATINUM", 
        "discription": "PLATINUM - $50,000 reward\nTotal: 70+ users (direct or indirect)", 
        "threshold_amount": 300000, 
        "bonus_amount": 50000, 
        "tier_level": "4",
        "required_direct_users": None,
        "required_total_users": 70,
        "required_deposit": 300000,
        "reward_description": "$50,000 reward\nTotal: 70+ users (direct or indirect)"
    },
    {
        "name": "DIAMOND", 
        "discription": "DIAMOND - $100,000 + BYD CAR Massive reward\nfor top performers!", 
        "threshold_amount": 500000, 
        "bonus_amount": 100000, 
        "tier_level": "5",
        "required_direct_users": None,
        "required_total_users": 100,
        "required_deposit": 500000,
        "reward_description": "$100,000 + BYD CAR Massive reward\nfor top performers!"
    }
]




def create_initial_referral_levels(db: Session):
    print("Attempting to seed initial referral level percentages...")
    try:
        existing_levels_query = db.query(ReferralLevelPercentage.level)
        existing_levels = {level_row[0] for level_row in existing_levels_query.all()}
        
        levels_to_add = []
        for level_data in REFERRAL_LEVELS_DATA:
            if level_data["level"] not in existing_levels:
                new_level = ReferralLevelPercentage(
                    level=level_data["level"],
                    percentage=level_data["percentage"],
                    description=level_data["description"]
                    # created_at and updated_at might be handled by your BaseModel
                )
                levels_to_add.append(new_level)
                print(f"  Queueing Level {level_data['level']} with {level_data['percentage']}% for creation.")
            else:
                # print(f"  Level {level_data['level']} already exists. Skipping.")
                pass

        if levels_to_add:
            db.add_all(levels_to_add)
            db.commit()
            for level in levels_to_add:
                db.refresh(level) # To get any DB-generated values like auto-increment IDs if not level itself
            print(f"Successfully added {len(levels_to_add)} new referral level percentages.")
        else:
            print("All referral level percentages already exist. No new levels added.")

    except Exception as e:
        print(f"Error during initial referral level seeding: {e}")
        db.rollback() # Rollback in case of any error during the process
    finally:
        # The session passed to this function should be managed by the caller in the startup event
        pass
    
    
def create_initial_ranks(db: Session):
    print("📦 Seeding ranks...")
    try:
        existing_ranks = {row[0] for row in db.query(Rank.name).all()}
        new_ranks = []

        for data in RANKS_DATA:
            if data["name"] not in existing_ranks:
                new_ranks.append(
                    Rank(
                        name=data["name"],
                        discription=data["discription"],
                        threshold_amount=data["threshold_amount"],
                        bonus_amount=data["bonus_amount"],
                        tier_level=data["tier_level"],
                        required_direct_users=data["required_direct_users"],
                        required_total_users=data["required_total_users"],
                        required_deposit=data["required_deposit"],
                        reward_description=data["reward_description"]
                    )
                )

        if new_ranks:
            db.add_all(new_ranks)
            db.commit()
            print(f"Added {len(new_ranks)} ranks.")
        else:
            print("Ranks already seeded.")

    except Exception as e:
        db.rollback()
        print(f"Error seeding ranks: {e}")
        
        
# def create_initial_referral_rewards(db: Session):
#     print("Creating initial referral rewards...")
#     try:
#         existing_rewards = {row[0] for row in db.query(MilestoneReward.id).all()}
#         new_rewards = []

#         for data in REWARD_DATA:
#             if data["id"] not in existing_rewards:
#                 new_rewards.append(
#                     MilestoneReward(
#                         id=data["id"],
#                         required_direct_users=data["required_direct_users"],
#                         required_total_users=data["required_total_users"],
#                         required_deposit=data["required_deposit"],
#                         reward_description=data["reward_description"]
#                     )
#                 )

#         if new_rewards:
#             db.add_all(new_rewards)
#             db.commit()
#             print(f"Added {len(new_rewards)} referral rewards.")
#         else:
#             print("Referral rewards already seeded.")

#     except Exception as e:
#         db.rollback()
#         print(f"Error seeding referral rewards: {e}")

# If you need to call this with a self-managed session (e.g., for testing this function):
# def init_db_main():
#     db = SessionLocal()
#     try:
#         create_initial_referral_levels(db)
#     finally:
#         db.close()
#
# if __name__ == "__main__":
#     init_db_main()
