from datetime import date, datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum
from pydantic.utils import GetterDict
from app.schemas.base import CustomBaseModel




class StatusEnum(str, Enum):
    pending = "pending"
    verified = "verified"
    rejected = "rejected"


class UserBase(CustomBaseModel):
    name: str
    dob: Optional[date] = None
    nationality: str
    country_of_residence: str
    preferred_currency: Optional[int] = None
    address: str
    country_code: str
    phone: str
    email: EmailStr
    national_id: str
    passport: str
    is_admin: bool = False
    is_active: bool = True
    is_kyc_verified: bool = False
    kyc_status: StatusEnum = StatusEnum.pending
    profile_picture: Optional[str] = None


class UserCreate(UserBase):
    referral_user_code: Optional[str] = None
    rank_id: int
    password: str 


class UserUpdate(PydanticBaseModel):
    name: Optional[str] = None
    nationality: Optional[str] = None
    country_of_residence: Optional[str] = None
    preferred_currency: Optional[int] = None
    address: Optional[str] = None
    country_code: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    profile_picture: Optional[str] = None



class UserOut(UserBase):  # Inherit UserBase but override if needed
    id: int
    referral_code: str
    referrer_name: Optional[str] = None
    kyc_status_reason: Optional[str] = None
    rank_name: Optional[str] = None
    preferred_currency_name: Optional[str] = None
    preferred_currency_code: Optional[str] = None
    current_total_referrals: Optional[float] = None
    created_at: datetime

    class Config:
        from_attributes = True



class Login(PydanticBaseModel):
    email: EmailStr
    password: str
    

class KYCStatusUpdate(PydanticBaseModel):
    kyc_status: StatusEnum
    kyc_status_reason: Optional[str] = None
