from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from app.schemas.base import CustomBaseModel

class DepositBase(CustomBaseModel):
    admin_wallet_id: str
    amount: float
    transaction_id: str
    status: Optional[str] = "pending"  # plain string, default to "pending"


class DepositCreate(DepositBase):
    pass


class DepositUpdate(BaseModel):
    status: Optional[str] = None


class DepositOut(DepositBase):
    id: int
    user_name: str
    reviewed_by_admin_name: Optional[str] = None
    approved_at: Optional[datetime] = None
    created_at: datetime  # inherited from BaseModel
    updated_at: datetime  # inherited from BaseModel

    class Config:
        from_attributes = True
