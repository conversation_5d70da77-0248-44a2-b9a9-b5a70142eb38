from sqlalchemy.orm import Session
from app.models.admin_wallet import AdminWallet
from app.schemas.admin_wallet import AdminWalletCreate, AdminWalletUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_admin_wallets(db: Session, params: PaginationParams) -> Page[AdminWallet]:
    query = db.query(AdminWallet).order_by(AdminWallet.id.desc())
    return paginate(query, params)

def get_admin_wallet(db: Session) -> Optional[AdminWallet]:
    # return db.query(AdminWallet).filter(AdminWallet.id == admin_wallet_id).first()
    return db.query(AdminWallet).order_by(AdminWallet.id.desc()).first()

def create_admin_wallet(db: Session, AdminWalletData: AdminWalletCreate) -> AdminWallet:
    db_admin_wallet = AdminWallet(**AdminWalletData.dict())
    db.add(db_admin_wallet)
    db.commit()
    db.refresh(db_admin_wallet)
    return db_admin_wallet

def update_admin_wallet(db: Session, admin_wallet_id: int, AdminWallet: AdminWalletUpdate) -> Optional[AdminWallet]:
    db_admin_wallet = get_admin_wallet(db, admin_wallet_id)
    if db_admin_wallet:
        update_data = AdminWallet.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_admin_wallet, key, value)
        db.commit()
        db.refresh(db_admin_wallet)
    return db_admin_wallet

def delete_admin_wallet(db: Session, admin_wallet_id: int) -> bool:
    db_admin_wallet = db.query(AdminWallet).filter(AdminWallet.id == admin_wallet_id).first()
    if db_admin_wallet:
        db.delete(db_admin_wallet)
        db.commit()
        return True
    return False