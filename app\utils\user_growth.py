from sqlalchemy import func
from datetime import datetime, timedelta
from app.models.users import User
from sqlalchemy.orm import Session
from app.models.deposits import Deposit
from dateutil.relativedelta import relativedelta

def get_user_growth(db: Session, filter_by: str = "month"):
    now = datetime.utcnow()

    if filter_by == "month":
        start_date = now.replace(day=1) - relativedelta(months=6)
        period_format = "%Y-%m"
        delta = relativedelta(months=1)
    elif filter_by == "year":
        start_date = now.replace(month=1, day=1, year=now.year - 3)
        period_format = "%Y"
        delta = relativedelta(years=1)
    else:
        raise ValueError("Invalid filter. Use 'month' or 'year'.")

    # Query the database for existing user counts
    period_sql = func.date_format(User.created_at, period_format)
    query = (
        db.query(
            period_sql.label("period"),
            func.count(User.id).label("count")
        )
        .filter(User.created_at >= start_date)
        .filter(User.is_admin == False)
        .group_by(period_sql)
        .order_by(period_sql)
    )
    results = query.all()

    # Map queried results to a dictionary for quick lookup
    result_dict = {row.period: row.count for row in results}

    # Generate all time periods between start_date and now
    current = start_date
    final_result = []
    while current <= now:
        period_str = current.strftime(period_format)
        final_result.append({
            "period": period_str,
            "count": result_dict.get(period_str, 0)
        })
        current += delta

    return final_result



def get_deposit_growth(db: Session, filter_by: str = "month"):
    now = datetime.utcnow()

    if filter_by == "month":
        start_date = now.replace(day=1) - relativedelta(months=6)
        period_format = "%Y-%m"
        delta = relativedelta(months=1)
    elif filter_by == "year":
        start_date = now.replace(month=1, day=1, year=now.year - 3)
        period_format = "%Y"
        delta = relativedelta(years=1)
    else:
        raise ValueError("Invalid filter. Use 'month' or 'year'.")

    # Query existing deposit sums and counts per period for approved deposits only
    period_sql = func.date_format(Deposit.created_at, period_format)
    query = (
        db.query(
            period_sql.label("period"),
            func.sum(Deposit.amount).label("total_amount"),
            func.count(Deposit.id).label("deposit_count")
        )
        .filter(Deposit.created_at >= start_date, Deposit.status == "approved")
        .group_by(period_sql)
        .order_by(period_sql)
    )
    results = query.all()

    # Map results to a dict for fast lookup
    result_dict = {
        row.period: {"total_amount": row.total_amount, "deposit_count": row.deposit_count} 
        for row in results
    }

    # Generate full list of periods and fill missing with zeroes
    current = start_date
    final_result = []
    while current <= now:
        period_str = current.strftime(period_format)
        data = result_dict.get(period_str, {"total_amount": 0, "deposit_count": 0})
        final_result.append({
            "period": period_str,
            "total_amount": float(data["total_amount"] or 0),
            "deposit_count": data["deposit_count"]
        })
        current += delta

    return final_result