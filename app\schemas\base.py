# app/schemas/base.py

from pydantic import BaseModel, field_serializer
from datetime import datetime
import pytz
from typing import Any

class CustomBaseModel(BaseModel):
    class Config:
        from_attributes = True

    # This is the Pydantic v2 way to customize serialization for specific field types
    @field_serializer('*', when_used='json')
    def serialize_datetime(self, value: Any, _info):
        if isinstance(value, datetime):
            ist = pytz.timezone("Asia/Kolkata")
            # Handle naive and aware datetime correctly
            if value.tzinfo is None:
                # If naive, assume it's UTC and localize
                value = pytz.UTC.localize(value)
            
            # Convert to IST
            value = value.astimezone(ist)
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return value

    # Keep the model_dump method for backward compatibility
    def model_dump(self, *args, **kwargs):
        print("model_dump called")
        data = super().model_dump(*args, **kwargs)
        ist = pytz.timezone("Asia/Kolkata")
    
        for key, value in data.items():
            if isinstance(value, datetime):
                # Handle naive and aware datetime correctly
                if value.tzinfo is None:
                    # If naive, assume it's UTC and localize
                    value = pytz.UTC.localize(value)
                
                # Convert to IST
                value = value.astimezone(ist)
                data[key] = value.strftime("%Y-%m-%d %H:%M:%S")
        return data


