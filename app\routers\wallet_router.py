from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.wallets import WalletCreate, WalletUpdate, WalletOut
from app.services.wallet_service import get_wallets, get_wallet, create_wallet, update_wallet, delete_wallet
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/wallets",
    tags=["wallets"]
)

@router.get("/", response_model=Page[WalletOut])
def read_wallets(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_wallets(db, params=params)

@router.get("/user", response_model=WalletOut)
def read_wallet(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    wallet = get_wallet(db, user_id=current_user.id)
    if wallet is None:
        raise HTTPException(status_code=404, detail="wallet not found")
    return wallet

@router.post("/", response_model=WalletOut, status_code=status.HTTP_201_CREATED)
def create_new_wallet(wallet: WalletCreate, db: Session = Depends(get_db)):
    return create_wallet(db=db, wallet=wallet)

@router.put("/{wallet_id}", response_model=WalletOut)
def update_existing_wallet(wallet_id: int, wallet: WalletUpdate, db: Session = Depends(get_db)):
    updated_wallet = update_wallet(db=db, wallet_id=wallet_id, wallet=wallet)
    if updated_wallet is None:
        raise HTTPException(status_code=404, detail="wallet not found")
    return updated_wallet

@router.delete("/{wallet_id}", status_code=status.HTTP_200_OK)
def delete_existing_wallet(wallet_id: int, db: Session = Depends(get_db)):
    success = delete_wallet(db=db, wallet_id=wallet_id)
    if not success:
        raise HTTPException(status_code=404, detail="wallet not found")
    return {"detail": "wallet deleted successfully"}