from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.terms_and_conditions import TermsAndConditionsCreate, TermsAndConditionsUpdate, TermsAndConditionsOut
from app.services.terms_and_condition_service import get_terms_and_conditions, get_terms_and_condition, create_terms_and_condition, update_terms_and_condition, delete_terms_and_condition
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/terms_and_conditions",
    tags=["terms_and_conditions"]
)

@router.get("/", response_model=Page[TermsAndConditionsOut])
def read_terms_and_conditions(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db)
):
    return get_terms_and_conditions(db, params=params)

@router.get("/{terms_and_condition_id}", response_model=TermsAndConditionsOut)
def read_terms_and_condition(terms_and_condition_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    terms_and_condition = get_terms_and_condition(db, terms_and_condition_id=terms_and_condition_id)
    if terms_and_condition is None:
        raise HTTPException(status_code=404, detail="terms_and_condition not found")
    return terms_and_condition

@router.post("/", response_model=TermsAndConditionsOut, status_code=status.HTTP_201_CREATED)
def create_new_terms_and_condition(terms_and_condition: TermsAndConditionsCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_terms_and_condition(db=db, terms_and_condition=terms_and_condition)

@router.put("/{terms_and_condition_id}", response_model=TermsAndConditionsOut)
def update_existing_terms_and_condition(terms_and_condition_id: int, terms_and_condition: TermsAndConditionsUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_terms_and_condition = update_terms_and_condition(db=db, terms_and_condition_id=terms_and_condition_id, terms_and_condition=terms_and_condition)
    if updated_terms_and_condition is None:
        raise HTTPException(status_code=404, detail="terms_and_condition not found")
    return updated_terms_and_condition

@router.delete("/{terms_and_condition_id}", status_code=status.HTTP_200_OK)
def delete_existing_terms_and_condition(terms_and_condition_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_terms_and_condition(db=db, terms_and_condition_id=terms_and_condition_id)
    if not success:
        raise HTTPException(status_code=404, detail="terms_and_condition not found")
    return {"detail": "terms_and_condition deleted successfully"}