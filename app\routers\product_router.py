from fastapi import APIRouter, Depends, HTTPException, status, Form, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional
from app.schemas.products import ProductCreate, ProductUpdate, ProductOut
from app.services.product_service import get_products, get_product, create_product, update_product, delete_product
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user
from app.utils.save_image_to_local import save_image


router = APIRouter(
    prefix="/products",
    tags=["products"]
)

@router.get("/", response_model=Page[ProductOut])
def read_products(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_products(db, params=params)

@router.get("/{product_id}", response_model=ProductOut)
def read_product(product_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    product = get_product(db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="Product not found")
    return product

@router.post("/", response_model=ProductOut, status_code=status.HTTP_201_CREATED)
def create_new_product(name: str = Form(...),
                    description: str = Form(...),
                    image_url: Optional[UploadFile] = File(None),
                    is_active: bool = Form(True), 
                    db: Session = Depends(get_db), 
                    current_user=Depends(get_current_user)):
    
    image_path = save_image(image_url) if image_url else None
    
    product_data = ProductCreate(
        name=name,
        description=description,
        image_url=image_path,
        is_active=is_active
    )
    
    return create_product(db=db, product=product_data)

@router.put("/{product_id}", response_model=ProductOut)
def update_existing_product(
    product_id: int,
    name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    is_active: Optional[bool] = Form(None),
    image_url: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Optional image saving logic
    image_path = save_image(image_url) if image_url else None

    update_data = {
        "name": name,
        "description": description,
        "is_active": is_active,
        "image_url": image_path
    }
    # Remove keys with None to avoid overwriting unchanged fields
    update_data = {k: v for k, v in update_data.items() if v is not None}

    updated_product = update_product(db=db, product_id=product_id, update_data=update_data)
    if updated_product is None:
        raise HTTPException(status_code=404, detail="Product not found")
    return updated_product

@router.delete("/{product_id}", status_code=status.HTTP_200_OK)
def delete_existing_product(product_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_product(db=db, product_id=product_id)
    if not success:
        raise HTTPException(status_code=404, detail="Product not found")
    return {"detail": "Product deleted successfully"}