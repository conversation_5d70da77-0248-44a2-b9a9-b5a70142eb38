from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum
from app.schemas.base import CustomBaseModel




class TransactionLogBase(CustomBaseModel):
    user_name: str
    transaction_type: str
    reference_id: Optional[str] = None
    amount: float
    description: str
    # remarks: Optional[str] = None

class TransactionLogCreate(TransactionLogBase):
    pass

class TransactionLogOut(TransactionLogBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True