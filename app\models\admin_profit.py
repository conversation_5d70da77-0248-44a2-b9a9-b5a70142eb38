from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey

class AdminProfit(BaseModel):
    __tablename__ = "admin_profits"
    
    id = Column(Integer, primary_key=True)
    today_rate = Column(Float, nullable=False)
    total_user_getting_profit = Column(Integer, nullable=False)
    total_net_deposit = Column(String(100), nullable=False)
    distributed_amount = Column(String(100), nullable=False)