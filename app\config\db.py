from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from app.config.config import settings

engine = create_engine(
    settings.DATABASE_URL, 
    pool_size=50,         # Set the pool size higher (default is 5 or 10)
    max_overflow=70,
    pool_timeout=60,
    )
SessionLocal = sessionmaker(autocommit = False, autoflush = False, bind = engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close