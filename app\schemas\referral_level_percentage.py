from typing import Optional
from pydantic import BaseModel
from decimal import Decimal
from app.schemas.base import CustomBaseModel

class ReferralLevelPercentageBase(CustomBaseModel):
    percentage: Decimal
    description: Optional[str] = None


class ReferralLevelPercentageCreate(ReferralLevelPercentageBase):
    level: int  # include level in create since it's primary key


class ReferralLevelPercentageUpdate(BaseModel):
    percentage: Optional[Decimal] = None
    description: Optional[str] = None


class ReferralLevelPercentageOut(ReferralLevelPercentageBase):
    level: int

    class Config:
        orm_mode = True
