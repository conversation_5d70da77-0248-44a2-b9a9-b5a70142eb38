from sqlalchemy.orm import Session
from app.models.wallets import Wallet
from app.schemas.wallets import WalletCreate, WalletUpdate, WalletOut
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from sqlalchemy.orm import joinedload

def get_wallets(db: Session, params: PaginationParams) -> Page[Wallet]:
    query = db.query(Wallet).options(joinedload(Wallet.user)).order_by(Wallet.id.desc())
    return paginate(query, params)

def get_wallet(db: Session, user_id: int) -> Optional[Wallet]:
    wallet = db.query(Wallet).filter(Wallet.user_id == user_id).first()

    return WalletOut(
        user_name=wallet.user.name if wallet.user else None,
        balance=wallet.balance or 0.0,
        total_profit=wallet.total_profit or 0.0,
        total_referral_bonus=wallet.total_referral_bonus or 0.0,
        total_deposits=wallet.total_deposits or 0.0,
        eligible_amount=wallet.eligible_amount or 0.0,
        current_deposits=wallet.current_deposits or 0.0,
        current_profit=wallet.current_profit or 0.0,
        current_referral_bonus=wallet.current_referral_bonus or 0.0,
        total_earnings=wallet.total_profit + wallet.total_referral_bonus or 0.0
    )

def create_wallet(db: Session, user_id, balance, total_referral_bonus, total_profit) -> Wallet:
    db_wallet = Wallet(user_id=user_id,
                       balance=balance,
                       total_referral_bonus=total_referral_bonus,
                       total_profit=total_profit)
    db.add(db_wallet)
    db.commit()
    db.refresh(db_wallet)
    return db_wallet

def update_wallet(db: Session, wallet_id: int, Wallet: WalletUpdate) -> Optional[Wallet]:
    db_wallet = get_wallet(db, wallet_id)
    if db_wallet:
        update_data = Wallet.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_wallet, key, value)
        db.commit()
        db.refresh(db_wallet)
    return db_wallet

def delete_wallet(db: Session, wallet_id: int) -> bool:
    db_wallet = db.query(Wallet).filter(Wallet.id == wallet_id).first()
    if db_wallet:
        db.delete(db_wallet)
        db.commit()
        return True
    return False