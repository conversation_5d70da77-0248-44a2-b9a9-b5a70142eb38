from datetime import datetime
from typing import Optional
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.dialects.mysql import LONGTEXT




class PrivacyAndPolicy(BaseModel):
    __tablename__ = "privacy_and_policy"

    id = Column(Integer, primary_key=True)
    title = Column(String(500), nullable=False)
    description = Column(LONGTEXT, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)