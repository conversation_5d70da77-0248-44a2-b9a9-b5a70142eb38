from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel
from app.schemas.base import CustomBaseModel


class AdminProfitBase(CustomBaseModel):
    today_rate: float
    total_user_getting_profit: int
    total_net_deposit: str
    distributed_amount: str
    # You can also add `active: bool = True` if needed

class AdminProfitCreate(PydanticBaseModel):
    today_rate: float
    created_at: Optional[datetime] = None

class AdminProfitUpdate(PydanticBaseModel):
    today_rate: Optional[float] = None

class AdminProfitOut(AdminProfitBase):
    id: int
    total_current_deposits: Optional[float] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True
