"""rank mopdel

Revision ID: 1da0352f0a11
Revises: e602f7267231
Create Date: 2025-05-30 16:02:35.526310

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '1da0352f0a11'
down_revision: Union[str, None] = 'e602f7267231'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ranks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('discription', sa.Text(), nullable=True),
    sa.Column('threshold_amount', sa.Float(), nullable=False),
    sa.Column('bonus_amount', sa.Float(), nullable=False),
    sa.Column('tier_level', sa.String(length=100), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.add_column('users', sa.Column('rank_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'users', 'ranks', ['rank_id'], ['id'])
    op.drop_column('users', 'rank')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('rank', mysql.ENUM('bronze', 'silver', 'gold', 'platinum'), nullable=True))
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'rank_id')
    op.drop_table('ranks')
    # ### end Alembic commands ###
