from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.models.users import User
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user, is_admin_user
from app.models.wallets import Wallet
from app.models.admin_profit import AdminProfit
from app.utils.user_rank_update import update_rank_and_bonus
from app.models.profit_logs import ProfitLog
from sqlalchemy import func, text
from app.models.withdrawals import Withdrawal
from app.utils.user_growth import get_user_growth, get_deposit_growth
from fastapi import Query
from app.models.user_ranks import Rank


router = APIRouter(
    prefix="/dashboard",
    tags=["dashboard"]
)

@router.get("/")
def read_dashboard(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    user = db.query(User).filter(User.id == current_user.id).first()
    wallet = user.wallet

    user_balance = wallet.balance if wallet else 0
    user_total_profit = wallet.total_profit if wallet else 0
    user_deposit = wallet.current_deposits if wallet else 0
    total_referral_bonus = wallet.total_referral_bonus if wallet else 0

    admin_profit_rate = db.query(AdminProfit).order_by(AdminProfit.id.desc()).first()
    today_profit_rate = admin_profit_rate.today_rate if admin_profit_rate else 0
    today_profit_amount = wallet.current_profit if wallet else 0

    # Total profit percentage
    total_profit_percent = (
        db.query(func.sum(ProfitLog.profit_percent))
        .filter(ProfitLog.user_id == user.id)
        .scalar() or 0.0
    )

    # Get current rank and progress
    # rank = user.rank
    rank = db.query(Rank).filter(Rank.id == user.rank_id).first()
    ranks = db.query(Rank).order_by(Rank.threshold_amount.asc()).all()

    # Total deposits: self + legs
    user_total_deposit = sum(d.amount for d in user.deposits if d.status == "approved")
    leg_total_deposit = user.leg_deposit_total or 0.0
    total_deposit_progress = leg_total_deposit

    # Find next rank and amount needed
    next_rank = None
    amount_to_next_rank = 0
    
    if rank:
        found_current = False
        for r in ranks:
            if found_current:
                next_rank = r
                amount_to_next_rank = rank.required_deposit if rank else 0
                break
            if r.id == rank.id:
                found_current = True
    else:
        # If no current rank assigned, treat the first one as current
        if ranks:
            next_rank = ranks[1] if len(ranks) > 1 else None
            amount_to_next_rank = rank.required_deposit if rank else 0


    return {
        "balance": user_balance,
        "total_profit": user_total_profit,
        "today_profit_rate": today_profit_rate,
        "current_deposit": user_deposit,
        "today_profit_amount": today_profit_amount,
        "rank": rank.name,
        "total_deposit_progress": total_deposit_progress,
        "next_rank": next_rank.name if next_rank else None,
        "amount_to_next_rank": amount_to_next_rank,
        "referral_link": user.referral_code,
        "total_referral_bonus": total_referral_bonus,
        "total_profit_percent": total_profit_percent,
        "total_indirect_users": (user.leg_user_count or 0) - (user.direct_user_count or 0),
        "total_direct_users": user.direct_user_count or 0,
        "total_leg_users": user.leg_user_count or 0,
        "required_direct_users" : rank.required_direct_users,
        "required_total_users" : rank.required_total_users,
        "required_indirect_users" : (rank.required_total_users if rank.required_total_users else 0) - (rank.required_direct_users if rank.required_direct_users else 0)
    }





@router.get("/admin", response_model=dict)
def read_admin_dashboard(db: Session = Depends(get_db), current_user=Depends(is_admin_user)):
    total_users = db.query(User).filter(User.is_admin == False).count()
    total_current_deposits = db.query(Wallet).with_entities(func.sum(Wallet.current_deposits)).scalar() or 0.0
    total_deposits = db.query(Wallet).with_entities(func.sum(Wallet.total_deposits)).scalar() or 0.0
    today_profit_obj = db.query(AdminProfit).order_by(AdminProfit.id.desc()).first()
    yesterday_profit_obj = db.query(AdminProfit).order_by(AdminProfit.id.desc()).offset(1).first()

    today_profit_rate = today_profit_obj.today_rate if today_profit_obj else 0.0
    yesterday_profit_rate = yesterday_profit_obj.today_rate if yesterday_profit_obj else 0.0
    profit_difference = today_profit_rate - yesterday_profit_rate
    total_withdrawal = (db.query(func.sum(Withdrawal.amount)).filter(Withdrawal.status == "approved").scalar() or 0.0)
    
    pending_kyc_user_count = db.query(User).filter(User.kyc_status == "pending").count()
    return {
        "total_users": total_users,
        "total_current_deposits": total_current_deposits,
        "today_profit_rate": today_profit_rate,
        "profit_difference": profit_difference,
        "total_withdrawal": total_withdrawal,
        "total_deposits": total_deposits,
        "pending_kyc_user_count": pending_kyc_user_count
    }

@router.get("/user/growth")
def user_growth(filter: str = Query("month", enum=["month", "year"]), db: Session = Depends(get_db)):
    data = get_user_growth(db, filter_by=filter)
    return data
    
@router.get("/deposit/growth")
def deposit_growth(
    filter: str = Query("month", enum=["month", "year"]),
    db: Session = Depends(get_db)
):
    data = get_deposit_growth(db, filter_by=filter)
    return data


@router.get("/referral/header")
def referral_header(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    wallet = db.query(Wallet).filter(Wallet.user_id == current_user.id).first()
    total_referral_bonus = wallet.total_referral_bonus if wallet else 0.0

    leg_users_query = text("""
        WITH RECURSIVE referral_tree AS (
            SELECT id, is_active FROM users WHERE referrer_id = :user_id
            UNION ALL
            SELECT u.id, u.is_active
            FROM users u
            INNER JOIN referral_tree rt ON u.referrer_id = rt.id
        )
        SELECT 
            COUNT(*) AS total_leg_users,
            SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) AS total_active_leg_users
        FROM referral_tree;
    """)

    result = db.execute(leg_users_query, {"user_id": current_user.id}).fetchone()

    return {
        "total_leg_users": result.total_leg_users,
        "total_active_leg_users": result.total_active_leg_users,
        "total_referral_bonus": total_referral_bonus
    }
