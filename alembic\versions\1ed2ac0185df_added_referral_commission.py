"""added referral_commission

Revision ID: 1ed2ac0185df
Revises: ada91a69491b
Create Date: 2025-05-22 18:24:51.900800

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ed2ac0185df'
down_revision: Union[str, None] = 'ada91a69491b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('referral_level_percentages',
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('percentage', sa.Numeric(precision=4, scale=2), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('level')
    )
    op.create_table('referral_commissions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('deposit_id', sa.Integer(), nullable=False),
    sa.Column('depositing_user_id', sa.Integer(), nullable=False),
    sa.Column('recipient_user_id', sa.Integer(), nullable=False),
    sa.Column('commission_level', sa.Integer(), nullable=False),
    sa.Column('commission_percentage_applied', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('commission_amount', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('status', sa.Enum('PENDING_PROCESSING', 'SUCCESS_CREDITED', 'ERROR_NO_WALLET', 'ERROR_CREDITING_FAILED', 'ERROR_CALCULATION', name='commissioncreditstatus'), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['deposit_id'], ['deposits.id'], ),
    sa.ForeignKeyConstraint(['depositing_user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['recipient_user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_referral_commissions_id'), 'referral_commissions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_referral_commissions_id'), table_name='referral_commissions')
    op.drop_table('referral_commissions')
    op.drop_table('referral_level_percentages')
    # ### end Alembic commands ###
