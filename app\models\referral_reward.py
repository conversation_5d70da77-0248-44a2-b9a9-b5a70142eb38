from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey
from app.config.db import get_db





# class MilestoneReward(BaseModel):
#     __tablename__ = "milestone_rewards"

#     id = Column(Integer, primary_key=True)
#     required_direct_users = Column(Integer)
#     required_total_users = Column(Integer)
#     required_deposit = Column(Float)
#     reward_description = Column(String(500))
    
    
    
    
class UserRewardProgress(BaseModel):
    __tablename__ = "user_reward_progress"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    rank_id = Column(Integer, ForeignKey("ranks.id"))
    is_achieved = Column(Boolean, default=True)
    achieved_on = Column(DateTime, default=datetime.utcnow)
    
    rank = relationship("Rank", back_populates="reward_records")
    
    
    
def get_db_session():
    return get_db().__next__()