from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum
from app.schemas.base import CustomBaseModel



class WithdrawalBase(CustomBaseModel):
    user_name: str
    wallet_address: str
    amount: float
    network: str
    status: Optional[str] = None
    approved_at: Optional[datetime] = None

class WithdrawalCreate(PydanticBaseModel):
    wallet_address: str
    amount: float
    network: str

class WithdrawalUpdate(PydanticBaseModel):
    status: Optional[str] = None

class WithdrawalOut(WithdrawalBase):
    id: int
    user_balance : Optional[float] = None
    created_at: datetime

    class Config:
        from_attributes = True 