"""currency conversion model fk added to user

Revision ID: 85bad136a8a4
Revises: 8b539fba5981
Create Date: 2025-06-02 18:47:31.565760

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '85bad136a8a4'
down_revision: Union[str, None] = '8b539fba5981'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('privacy_and_policy', 'description',
               existing_type=mysql.TEXT(),
               type_=mysql.LONGTEXT(),
               existing_nullable=False)
    op.alter_column('terms_and_conditions', 'description',
               existing_type=mysql.TEXT(),
               type_=mysql.LONGTEXT(),
               existing_nullable=False)
    op.alter_column('transaction_ledger', 'reference_id',
               existing_type=mysql.INTEGER(),
               type_=sa.String(length=300),
               existing_nullable=True)
    op.alter_column('users', 'preferred_currency',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.Integer(),
               nullable=True)
    op.create_foreign_key(None, 'users', 'currency_conversions', ['preferred_currency'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.alter_column('users', 'preferred_currency',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=100),
               nullable=False)
    op.alter_column('transaction_ledger', 'reference_id',
               existing_type=sa.String(length=300),
               type_=mysql.INTEGER(),
               existing_nullable=True)
    op.alter_column('terms_and_conditions', 'description',
               existing_type=mysql.LONGTEXT(),
               type_=mysql.TEXT(),
               existing_nullable=False)
    op.alter_column('privacy_and_policy', 'description',
               existing_type=mysql.LONGTEXT(),
               type_=mysql.TEXT(),
               existing_nullable=False)
    # ### end Alembic commands ###
