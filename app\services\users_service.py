from sqlalchemy.orm import Session
from app.models.users import User, StatusEnum
from app.schemas.users import UserCreate, UserUpdate, KYCStatusUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.utils.auth_utility import hash_password, generate_referral_code
from app.services.wallet_service import create_wallet
from sqlalchemy import text
from app.utils.referral_trees import build_referral_tree
import smtplib
from email.mime.text import MIMEText
from fastapi import UploadFile, HTTPException, status
from app.utils.save_image_to_local import save_image
from sqlalchemy.orm import joinedload
from app.tasks import handle_user_signup
from app.utils.email import send_email

def get_users(
    db: Session, 
    params: PaginationParams, 
    status: Optional[str] = None, 
    search: Optional[str] = None
) -> Page[User]:
    query = (
        db.query(User)
        .options(
            joinedload(User.rank),
            joinedload(User.referrer),
            joinedload(User.currency)
        )
        .filter(User.is_deleted == False)
        .filter(User.is_admin == False)
        .order_by(User.id.desc())
    )

    if status:
        query = query.filter(User.kyc_status == status)

    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (User.name.ilike(search_term)) |
            (User.email.ilike(search_term))
        )

    return paginate(query, params)

def get_user(db: Session, User_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == User_id, User.is_deleted == False).first()

def get_token_user(db: Session, User_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == User_id).first()

def create_user(db: Session, user: UserCreate) -> User:
    # this is to get the reffered user's id and store it in referrer_id
    
    existing_user = db.query(User).filter(User.email == user.email).first()
    if existing_user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,detail="User with this email already exists")
    
    referral_user_id = None
    if user.referral_user_code is not None:
        user_referral_code = db.query(User).filter(User.referral_code == user.referral_user_code).first()
        referral_user_id = user_referral_code.id if user_referral_code else None
    
    #this is to generate a unique referral code for the new user
    generated_referral_code = generate_referral_code()
    while db.query(User).filter(User.referral_code == generated_referral_code).first():
        generated_referral_code = generate_referral_code()

    user_data = user.dict(exclude={"password", "referral_code", "referral_user_code", "referrer_id"})

    db_User = User(
        **user_data,
        password=hash_password(user.password),
        referral_code=generated_referral_code,
        referrer_id=referral_user_id
    )
    db.add(db_User)
    db.commit()
    db.refresh(db_User)
    
    admin_mail = db.query(User).filter(User.is_admin == True).first().email
    
    subject = f"New Signup Alert: {db_User.name}"
    body = f"{db_User.name} has signed up with the email {db_User.email}."
    send_email(subject=subject, recipient_email=admin_mail, body=body)
    
    db_User = db.query(User).options(joinedload(User.rank)).filter(User.id == db_User.id).first()
    
    if db_User.is_admin is False:
        new_wallet = create_wallet(db, user_id=db_User.id, balance=0, total_referral_bonus=0, total_profit=0)
        
    if db_User.referrer_id:
        handle_user_signup.delay(db_User.id)
    return db_User

def update_user(db: Session, User_id: int, User: UserUpdate) -> Optional[User]:
    db_User = get_user(db, User_id)
    if db_User:
        update_data = User.dict(exclude_none=True)
        for key, value in update_data.items():
            setattr(db_User, key, value)
        db.commit()
        db.refresh(db_User)
    return db_User

def delete_user(db: Session, User_id: int) -> bool:
    db_User = get_user(db, User_id)
    if db_User:
        db_User.is_active = False
        db_User.is_deleted = True
        db.commit()
        return True
    return False


def get_user_referrals(db: Session, user_id: int) -> Optional[User]:
    
    main_user = get_user(db, user_id)
    if main_user:
        # direct_leg_users = db.query(User).filter(User.referrer_id == main_user.id).all()
        sql = text("""
    WITH RECURSIVE referral_tree AS (
        SELECT id AS user_id, referrer_id, 0 AS level
        FROM users
        WHERE id = :root_user_id AND is_active = TRUE AND is_deleted = FALSE
        UNION ALL
        SELECT u.id, u.referrer_id, rt.level + 1
        FROM users u
        INNER JOIN referral_tree rt ON u.referrer_id = rt.user_id
        WHERE u.is_active = TRUE AND u.is_deleted = FALSE
    ),
    user_deposits AS (
        SELECT user_id, SUM(amount) AS total_deposit
        FROM deposits
        WHERE status = 'approved'
        GROUP BY user_id
    ),
    user_commissions AS (
        SELECT depositing_user_id AS user_id, SUM(commission_amount) AS commission_to_root
        FROM referral_commissions
        WHERE recipient_user_id = :root_user_id
        GROUP BY depositing_user_id
    )
    SELECT 
        rt.user_id,
        rt.referrer_id,
        rt.level,
        u.name,
        COALESCE(ud.total_deposit, 0) AS total_deposit,
        COALESCE(uc.commission_to_root, 0) AS commission_to_root
    FROM referral_tree rt
    JOIN users u ON u.id = rt.user_id
    LEFT JOIN user_deposits ud ON ud.user_id = rt.user_id
    LEFT JOIN user_commissions uc ON uc.user_id = rt.user_id
    ORDER BY rt.level, rt.user_id;
    """)



        result = db.execute(sql, {"root_user_id": user_id}).mappings().all()
        referral_tree = build_referral_tree(user_id, result)
        return referral_tree
    return [{"detail":"user not found"}]


def update_kyc_status(db: Session, user_id: int, kyc_status: KYCStatusUpdate):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        db_user.kyc_status = kyc_status.kyc_status
        db_user.kyc_status_reason = kyc_status.kyc_status_reason
    if kyc_status.kyc_status.lower() == "verified":
        db_user.is_kyc_verified = True
    elif kyc_status.kyc_status.lower() == "rejected" or "pending":
        db_user.is_kyc_verified = False
    db.commit()
    db.refresh(db_user)
    return db_user


def send_otp_to_email(email: str, otp: int):
    sender_email = "<EMAIL>"
    password = "your_password"
    
    message = MIMEText(f"Your OTP is: {otp}")
    message['Subject'] = "Email Verification OTP"
    message['From'] = sender_email
    message['To'] = email

    with smtplib.SMTP_SSL("smtp.gmail.com", 465) as server:
        server.login(sender_email, password)
        server.send_message(message)
        
def request_verification(db: Session, user_id: int, national_id: UploadFile, passport: UploadFile):
    db_user = db.query(User).filter(User.id == user_id).first()
    national_id_path = save_image(national_id) if national_id else None
    passport_path = save_image(passport) if passport else None
    if db_user:
        if national_id_path:
            db_user.national_id = national_id_path
        if passport_path:
            db_user.passport = passport_path
        db_user.kyc_status = StatusEnum.pending
        db.commit()
        db.refresh(db_user)
        return db_user
    return None