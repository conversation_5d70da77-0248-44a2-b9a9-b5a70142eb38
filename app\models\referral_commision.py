from app.models.base import BaseModel # Your existing BaseModel
from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

# New simplified CommissionStatus
class CommissionCreditStatus(str, enum.Enum):
    PENDING_PROCESSING = "pending_processing" # Initial state before attempting credit
    SUCCESS_CREDITED = "success_credited"     # Successfully calculated and credited
    ERROR_NO_WALLET = "error_no_wallet"       # Recipient wallet not found
    ERROR_CREDITING_FAILED = "error_crediting_failed" # DB error or other issue during credit
    ERROR_CALCULATION = "error_calculation"     # Issue during commission calculation (e.g., no rate)
    # SKIPPED_ZERO_AMOUNT = "skipped_zero_amount" # Optional: if you want to log zero amount commissions

class ReferralCommission(BaseModel):
    __tablename__ = "referral_commissions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    deposit_id = Column(Integer, ForeignKey("deposits.id"), nullable=False)
    depositing_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    recipient_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    commission_level = Column(Integer, nullable=False)
    commission_percentage_applied = Column(Numeric(5, 2), nullable=False)
    commission_amount = Column(Numeric(12, 2), nullable=False)
    # Status reflects the outcome of the automated crediting attempt
    status = Column(SQLEnum(CommissionCreditStatus), default=CommissionCreditStatus.PENDING_PROCESSING, nullable=False)
    # Optional: add a notes field for errors
    # processing_notes = Column(String(255), nullable=True)

    # Relationships (as defined before)
    deposit_source = relationship("Deposit", back_populates="referral_commissions")
    recipient = relationship("User", foreign_keys=[recipient_user_id], back_populates="referral_commissions_received")
    original_depositor_info = relationship("User", foreign_keys=[depositing_user_id], back_populates="referral_commissions_given")

    def __repr__(self):
        return f"<ReferralCommission id={self.id} recipient_user_id={self.recipient_user_id} amount={self.commission_amount} status='{self.status}'>"