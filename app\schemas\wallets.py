from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel as PydanticBaseModel, EmailStr
from enum import Enum
from app.schemas.base import CustomBaseModel


class WalletBase(CustomBaseModel):
    user_name: str
    balance: float = 0.0
    total_profit: float = 0.0
    total_referral_bonus: float = 0.0
    total_deposits: float = 0.0
    eligible_amount: float = 0.0
    current_deposits: float = 0.0
    current_profit: float = 0.0
    current_referral_bonus: float = 0.0 
    total_earnings: float = 0.0

class WalletCreate(WalletBase):
    pass

class WalletUpdate(PydanticBaseModel):
    balance: Optional[float] = None
    total_profit: Optional[float] = None
    total_referral_bonus: Optional[float] = None

class WalletOut(WalletBase):
    class Config:
        orm_mode = True