import anyio
import os
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from dotenv import load_dotenv
from pydantic import EmailStr

load_dotenv()

conf = ConnectionConfig(
    MAIL_USERNAME=os.getenv('MAIL_USERNAME'),
    MAIL_PASSWORD=os.getenv('MAIL_PASSWORD'),
    MAIL_FROM=os.getenv('MAIL_FROM'),
    MAIL_PORT=int(os.getenv('MAIL_PORT', 587)),
    MAIL_SERVER=os.getenv('MAIL_SERVER', 'smtp.gmail.com'),
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True,
)

def send_email(subject: str, recipient_email: EmailStr, body: str):
    """
    Sends an email synchronously from anywhere in the project.
    """
    message = MessageSchema(
        subject=subject,
        recipients=[recipient_email],
        body=body,
        subtype="plain"
    )

    async def send_async():
        fm = FastMail(conf)
        await fm.send_message(message)

    anyio.run(send_async)