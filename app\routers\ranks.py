from fastapi import Form, File, UploadFile, Depends, status, APIRouter, HTTPException
from sqlalchemy.orm import Session
from typing import List
# from app.schemas.ranks import RankCreate, RankUpdate, RankOut
# from app.services.rank_service import get_ranks, get_rank, create_rank, update_rank, delete_rank
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user
from app.models.user_ranks import Rank
# from app.models.referral_reward import MilestoneReward



router = APIRouter(
    prefix="/ranks",
    tags=["ranks"]
)



@router.get("/",)
def read_ranks(
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    ranks = db.query(Rank).all()
    
    result = []
    for rank in ranks:
        rank_dict = {
            "id": rank.id,
            "name": rank.name,
            "description": rank.discription,
            "threshold_amount": rank.threshold_amount,
            "bonus_amount": rank.bonus_amount,
            "tier_level": rank.tier_level,
            "reward_condition": {
                "required_direct_users": rank.required_direct_users,
                "required_total_users": rank.required_total_users,
                "required_deposit": rank.required_deposit,
                "reward_description": rank.reward_description
            } if rank.reward_description else None
        }
        result.append(rank_dict)
    
    return result

