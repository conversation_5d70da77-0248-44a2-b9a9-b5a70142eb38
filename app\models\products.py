from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.dialects.mysql import LONGTEXT


# PRODUCTS Table
class Product(BaseModel):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(LONGTEXT, nullable=False)
    image_url = Column(String(300))
    is_active = Column(Boolean, default=True)