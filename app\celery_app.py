# from celery import Celery
# from dotenv import load_dotenv
# import os

# load_dotenv()


# celery_app = Celery(
#     "my_app",
#     broker=os.getenv("CELERY_BROKER_URL"),
#     backend=os.getenv("CELERY_RESULT_BACKEND")
# )

# celery_app.conf.task_routes = {
#     'app.tasks.*': {'queue': 'reward'}
# }


# celery_app.conf.update(
#     task_serializer='json',
#     accept_content=['json'],
#     result_serializer='json',
#     timezone='Asia/Kolkata',
#     enable_utc=True,
# )



# app/celery_app.py
from celery import Celery
from dotenv import load_dotenv
import os

load_dotenv()

celery_app = Celery(
    "my_app",  # your app name
    broker=os.getenv("CELERY_BROKER_URL"),
    backend=os.getenv("CELERY_RESULT_BACKEND")
)

celery_app.conf.update(
    task_routes={"app.tasks.*": {"queue": "reward"}},
    broker_connection_retry_on_startup=True,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Kolkata',
    enable_utc=True,
)

celery_app.autodiscover_tasks(["app.tasks"])  # Automatically finds tasks in this module

