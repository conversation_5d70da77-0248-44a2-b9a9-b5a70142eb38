from sqlalchemy.orm import Session
from app.models.admin_profit import AdminProfit
from app.schemas.admin_profit import AdminProfitCreate, AdminProfitUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.models.users import User
from app.models.deposits import Deposit
from sqlalchemy import func
from app.services.wallet_service import update_wallet
from app.models.wallets import Wallet
from app.models.profit_logs import ProfitLog
from app.models.transaction_logs import TransactionLedger, TransactionType
from app.models.profit_logs import ProfitLog
from datetime import datetime
from fastapi import HTTPException
from sqlalchemy import or_


def get_admin_profits(db: Session, params: PaginationParams) -> Page[AdminProfit]:
    query = db.query(AdminProfit).order_by(AdminProfit.id.desc())
    return paginate(query, params)

def get_admin_profit(db: Session) -> Optional[AdminProfit]:
    admin_profit = db.query(AdminProfit).order_by(AdminProfit.id.desc()).first()
    total_current_deposits = db.query(func.sum(Wallet.current_deposits)).scalar() or 0.0
    
    return {
        "id": admin_profit.id,
        "today_rate": admin_profit.today_rate if admin_profit else 0.0,
        "total_user_getting_profit": admin_profit.total_user_getting_profit if admin_profit else 0,
        "total_net_deposit": admin_profit.total_net_deposit if admin_profit else 0.0,
        "distributed_amount": admin_profit.distributed_amount if admin_profit else 0.0,
        "created_at": admin_profit.created_at if admin_profit else None,
        "total_current_deposits": total_current_deposits
    }



def create_admin_profit(db: Session, AdminProfit_data: AdminProfitCreate) -> AdminProfit:
    # total_user_profit = db.query(User).filter(User.is_active == True).count()
    # total_deposit = db.query(func.sum(Deposit.amount)).filter(Deposit.status == "approved").scalar()
    # profit_rate = AdminProfit_data.today_rate / 100
    # total_distributed = db.query(func.sum(Deposit.amount * profit_rate)).filter(Deposit.status == "approved").scalar() or 0
    # db_admin_profit = AdminProfit(today_rate=AdminProfit_data.today_rate,
    #                               total_user_getting_profit=total_user_profit,
    #                               total_net_deposit=total_deposit,
    #                               distributed_amount=total_distributed)
    # db.add(db_admin_profit)
    # db.commit()
    # db.refresh(db_admin_profit)
    # wallet_amount_update = update_wallet(db=db, wallet_id=)
    # return db_admin_profit
    # 1. Get total active users
    today = datetime.now().date()

    if AdminProfit_data.created_at:
        existing_admin_profit = db.query(AdminProfit).filter(
            or_(
                func.date(AdminProfit.created_at) == today,
                func.date(AdminProfit.created_at) == AdminProfit_data.created_at.date()
            )
        ).first()
    else:
        existing_admin_profit = db.query(AdminProfit).filter(
            func.date(AdminProfit.created_at) == today
        ).first()
        
    if existing_admin_profit:
       raise HTTPException(status_code=400, detail="Admin profit for today or the given date already exists")
       
    total_user_profit = db.query(User).filter(User.is_active == True).count()

    # 2. Get total approved deposits
    total_deposit = db.query(func.sum(Wallet.current_deposits)).scalar() or 0

    # 3. Calculate profit rate
    profit_rate = AdminProfit_data.today_rate / 100

    # 4. Get total profit to distribute (just for logging)
    total_distributed = total_deposit * profit_rate

    # 5. Create the admin profit record
    db_admin_profit = AdminProfit(
        today_rate=AdminProfit_data.today_rate,
        total_user_getting_profit=total_user_profit,
        total_net_deposit=total_deposit,
        distributed_amount=total_distributed
    )
    
    if AdminProfit_data.created_at:
        db_admin_profit.created_at = AdminProfit_data.created_at
        
    db.add(db_admin_profit)

    # 6. Get each user's total approved deposit
    deposit_sums_query = db.query(
        Deposit.user_id,
        func.sum(Deposit.amount).label("total_deposit")
    ).filter(
        Deposit.status == "approved"
    ).group_by(Deposit.user_id)

    deposit_sums = {row.user_id: row.total_deposit for row in deposit_sums_query}

    # 7. Get all related wallets in one query
    wallet_query = db.query(Wallet).filter(Wallet.user_id.in_(deposit_sums.keys()))
    wallet_map = {wallet.user_id: wallet for wallet in wallet_query}

    # 8. Update each wallet
    for user_id, deposit_sum in deposit_sums.items():
        profit = deposit_sum * profit_rate
        
        # ✅ Create a profit log entry for this user
        profit_log = ProfitLog(
            user_id=user_id,
            profit_amount=profit,
            profit_percent=AdminProfit_data.today_rate
        )
        db.add(profit_log)
        
        new_transaction_log = TransactionLedger(
        user_id=user_id,
        amount=profit,
        transaction_type="daily_profit",
        reference_id=db_admin_profit.id, #no reference id found for daily admin profit
        description="Approved"
        # created_at is handled by BaseModel or server_default
        )
        db.add(new_transaction_log)

        if user_id in wallet_map:
            wallet = wallet_map[user_id]
            wallet.balance = (wallet.balance or 0.0) + profit
            wallet.total_profit = (wallet.total_profit or 0.0) + profit
            wallet.current_profit = (wallet.current_profit or 0.0) + profit
        else:
            # If wallet doesn't exist, create one
            new_wallet = Wallet(
                user_id=user_id,
                balance=profit,
                total_profit=profit,
                current_profit=profit,
            )
            db.add(new_wallet)

    # 9. Commit changes
    db.commit()
    db.refresh(db_admin_profit)
    db.refresh(new_transaction_log)

    return db_admin_profit

def update_admin_profit(db: Session, admin_profit_id: int, AdminProfit: AdminProfitUpdate) -> Optional[AdminProfit]:
    db_admin_profit = get_admin_profit(db, admin_profit_id)
    if db_admin_profit:
        update_data = AdminProfit.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_admin_profit, key, value)
        db.commit()
        db.refresh(db_admin_profit)
    return db_admin_profit

def delete_admin_profit(db: Session, admin_profit_id: int) -> bool:
    db_admin_profit = get_admin_profit(db, admin_profit_id)
    if db_admin_profit:
        db.delete(db_admin_profit)
        db.commit()
        return True
    return False