from typing import TypeVar, Generic, List
from sqlalchemy.orm import Query
from fastapi import Query as QueryParam
from pydantic import BaseModel
from math import ceil

T = TypeVar('T')

class PaginationParams:
    def __init__(
        self,
        skip: int = QueryParam(0, ge=0, description="Number of items to skip"),
        limit: int = QueryParam(10, ge=1, le=100, description="Number of items to return"),
        pagination: bool = QueryParam(True, description="Enable or disable pagination"),
    ):
        self.skip = skip
        self.limit = limit
        self.pagination = pagination

    @property
    def current_page(self) -> int:
        return (self.skip // self.limit) + 1 if self.limit else 1

class Page(BaseModel, Generic[T]):
    total_items: int
    skip: int
    limit: int
    current_page: int
    total_pages: int
    items: List[T]

    class Config:
        arbitrary_types_allowed = True

def paginate(query: Query, params: PaginationParams) -> Page:
    total_items = query.count()

    if not params.pagination:
        items = query.all()
        return Page(
            total_items=total_items,
            skip=0,
            limit=total_items,
            current_page=1,
            total_pages=1,
            items=items,
        )

    total_pages = ceil(total_items / params.limit) if params.limit else 1
    current_page = params.current_page
    items = query.offset(params.skip).limit(params.limit).all()

    return Page(
        total_items=total_items,
        skip=params.skip,
        limit=params.limit,
        current_page=current_page,
        total_pages=total_pages,
        items=items,
    )
