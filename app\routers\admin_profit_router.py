from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.schemas.admin_profit import AdminProfitCreate, AdminProfitUpdate, AdminProfitOut
from app.services.admin_profit_service import get_admin_profits, get_admin_profit, create_admin_profit, update_admin_profit, delete_admin_profit
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user


router = APIRouter(
    prefix="/admin_profits",
    tags=["admin_profits"]
)

@router.get("/", response_model=Page[AdminProfitOut])
def read_admin_profits(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_admin_profits(db, params=params)

@router.get("/header", response_model=AdminProfitOut)
def read_admin_profit(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    admin_profit = get_admin_profit(db)
    if admin_profit is None:
        raise HTTPException(status_code=404, detail="admin_profit not found")
    return admin_profit

@router.post("/", response_model=AdminProfitOut, status_code=status.HTTP_201_CREATED)
def create_new_admin_profit(admin_profit: AdminProfitCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_admin_profit(db=db, AdminProfit_data=admin_profit)

@router.put("/{admin_profit_id}", response_model=AdminProfitOut)
def update_existing_admin_profit(admin_profit_id: int, admin_profit: AdminProfitUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_admin_profit = update_admin_profit(db=db, admin_profit_id=admin_profit_id, admin_profit=admin_profit)
    if updated_admin_profit is None:
        raise HTTPException(status_code=404, detail="admin_profit not found")
    return updated_admin_profit

@router.delete("/{admin_profit_id}", status_code=status.HTTP_200_OK)
def delete_existing_admin_profit(admin_profit_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_admin_profit(db=db, admin_profit_id=admin_profit_id)
    if not success:
        raise HTTPException(status_code=404, detail="admin_profit not found")
    return {"detail": "admin_profit deleted successfully"}