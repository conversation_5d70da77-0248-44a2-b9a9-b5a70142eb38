"""combined rank and reward models

Revision ID: feae7d13af81
Revises: 0b90d02ba3fb
Create Date: 2025-06-10 20:28:32.380715

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'feae7d13af81'
down_revision: Union[str, None] = '0b90d02ba3fb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_reward_progress_ibfk_1', 'user_reward_progress', type_='foreignkey')
    op.drop_column('user_reward_progress', 'reward_id')
    op.drop_table('milestone_rewards')
    op.add_column('ranks', sa.Column('required_direct_users', sa.Integer(), nullable=True))
    op.add_column('ranks', sa.Column('required_total_users', sa.Integer(), nullable=True))
    op.add_column('ranks', sa.Column('required_deposit', sa.Float(), nullable=True))
    op.add_column('ranks', sa.Column('reward_description', sa.String(length=500), nullable=True))
    op.add_column('user_reward_progress', sa.Column('rank_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'user_reward_progress', 'ranks', ['rank_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_reward_progress', sa.Column('reward_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'user_reward_progress', type_='foreignkey')
    op.create_foreign_key('user_reward_progress_ibfk_1', 'user_reward_progress', 'milestone_rewards', ['reward_id'], ['id'])
    op.drop_column('user_reward_progress', 'rank_id')
    op.drop_column('ranks', 'reward_description')
    op.drop_column('ranks', 'required_deposit')
    op.drop_column('ranks', 'required_total_users')
    op.drop_column('ranks', 'required_direct_users')
    op.create_table('milestone_rewards',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('required_direct_users', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('required_total_users', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('required_deposit', mysql.FLOAT(), nullable=True),
    sa.Column('reward_description', mysql.VARCHAR(length=500), nullable=True),
    sa.Column('is_deleted', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
    sa.Column('created_at', mysql.DATETIME(), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
