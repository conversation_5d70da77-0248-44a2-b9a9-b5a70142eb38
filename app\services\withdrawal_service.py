from sqlalchemy.orm import Session
from app.models.withdrawals import Withdrawal
from app.schemas.withdrawals import WithdrawalCreate, WithdrawalUpdate, WithdrawalOut
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from sqlalchemy.sql import func
from app.models.users import User
from datetime import datetime, timedelta
from fastapi import HTTPException
from app.models.wallets import Wallet
from app.models.deposits import Deposit, DepositStatus
from app.models.transaction_logs import TransactionLedger, TransactionType
from sqlalchemy.orm import joinedload


def get_withdrawals(db: Session, params: PaginationParams, status: Optional[str] = None, search: Optional[str] = None) -> Page[WithdrawalOut]:
    # Original query
    query = (
        db.query(Withdrawal).join(Withdrawal.user)
        .options(
            joinedload(Withdrawal.user).joinedload(User.wallet)
        )
        .order_by(Withdrawal.id.desc())
    )
    
        # Apply status filter
    if status:
        query = query.filter(Withdrawal.status == status)

    # Apply search
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (Withdrawal.user.has(User.name.ilike(search_term)))
        )


    # Paginate the query
    paginated_data = paginate(query, params)

    # Modify the paginated results
    for w in paginated_data.items:
        wallet = w.user.wallet if w.user and w.user.wallet else None
        w.user_balance = (
            round((wallet.current_profit or 0.0) +
                  (wallet.current_referral_bonus or 0.0) +
                  (wallet.eligible_amount or 0.0), 2)
            if wallet else 0.0
        )

    return paginated_data


def get_user_withdrawals(db: Session, params: PaginationParams, user_id: int) -> Page[Withdrawal]:
    query = db.query(Withdrawal).options(joinedload(Withdrawal.user)).filter(Withdrawal.user_id == user_id).order_by(Withdrawal.id.desc())
    return paginate(query, params)

def get_withdrawal(db: Session, withdrawal_id: int) -> Optional[Withdrawal]:
    return db.query(Withdrawal).filter(Withdrawal.id == withdrawal_id).first()


def credit_eligible_deposits_to_wallet(db: Session, user_id: int) -> float:
    six_months_ago = datetime.utcnow() - timedelta(days=180)

    # Get deposits that are APPROVED, older than 6 months, and not yet credited
    eligible_deposits = db.query(Deposit).filter(
        Deposit.user_id == user_id,
        Deposit.status == DepositStatus.APPROVED,
        Deposit.approved_at != None,
        Deposit.approved_at <= six_months_ago
    ).all()

    total_eligible_amount = sum(d.amount for d in eligible_deposits)

    if total_eligible_amount > 0:
        # Update deposits status to CREDITED_TO_WALLET
        for deposit in eligible_deposits:
            deposit.status = DepositStatus.CREDITED_TO_WALLET

        # Update wallet eligible_amount
        wallet = db.query(Wallet).filter(Wallet.user_id == user_id).first()
        if wallet:
            wallet.eligible_amount = (wallet.eligible_amount or 0) + total_eligible_amount

        db.commit()

    return total_eligible_amount

def create_withdrawal(db: Session, withdrawal: WithdrawalCreate, user_id: int):
    
    # First credit any eligible deposits
    credit_eligible_deposits_to_wallet(db, user_id)

    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    if not db_user.is_kyc_verified:
        raise HTTPException(status_code=400, detail="Complete KYC verification to withdraw funds")

    db_wallet = db.query(Wallet).filter(Wallet.user_id == user_id).first()
    if not db_wallet:
        raise HTTPException(status_code=404, detail="Wallet not found")

    requested_amount = withdrawal.amount

    # Use eligible_amount instead of querying deposits
    total_available = (
        (db_wallet.current_profit or 0.0) +
        (db_wallet.current_referral_bonus or 0.0) +
        (db_wallet.eligible_amount or 0.0)
    )

    if total_available < requested_amount:
        raise HTTPException(
            status_code=200,
            detail={
                "message": "Insufficient funds",
                "requested_amount": requested_amount,
                "available_amount": round(total_available, 2),
                "breakdown": {
                    "current_profit": round(db_wallet.current_profit or 0.0, 2),
                    "current_referral_bonus": round(db_wallet.current_referral_bonus or 0.0, 2),
                    "eligible_deposits": round(db_wallet.eligible_amount or 0.0, 2)
                }
            }
        )
    
    approved_or_pending_withdrawals = db.query(Withdrawal).filter(
        Withdrawal.user_id == user_id,
        Withdrawal.status == "pending"
    ).all()

    total_pending_amount = sum(w.amount for w in approved_or_pending_withdrawals)
    print(f"this is total_pending_amount {total_pending_amount}")
    
    remaining_amount_to_withdraw = total_available - total_pending_amount
    print(f"remaining_amount_pending = {total_available} - {total_pending_amount} = {remaining_amount_to_withdraw}")
    print(f"this is remaining amount {remaining_amount_to_withdraw}")
    print(f"this is total available amount {total_available}")

    if requested_amount > remaining_amount_to_withdraw:
        raise HTTPException(status_code=400, detail="You have a pending withdrawal request that matches or exceeds your requested amount")

    # Create the pending withdrawal request (no wallet changes yet)
    db_withdrawal = Withdrawal(
        user_id=user_id,
        amount=requested_amount,
        wallet_address=withdrawal.wallet_address,
        network=withdrawal.network,
        status="pending"
    )

    db.add(db_withdrawal)
    db.commit()
    db.refresh(db_withdrawal)
    
    new_transaction_log = TransactionLedger(
    user_id=user_id,
    amount=withdrawal.amount,
    transaction_type="withdrawal",
    reference_id=withdrawal.wallet_address, # Will be available after flush/commit if not already
    description="Pending",
    created_at=db_withdrawal.created_at
    # created_at is handled by BaseModel or server_default
    )
    db.add(new_transaction_log)
    db.commit()
    db.refresh(new_transaction_log)
    

    return {
        "status": "success",
        "message": "Withdrawal request submitted and pending admin approval.",
        "withdrawal_id": db_withdrawal.id,
        "requested_amount": requested_amount
    }

# def update_withdrawal(db: Session, withdrawal_id: int, withdrawal: WithdrawalUpdate) -> Optional[Withdrawal]:
#     db_withdrawal = get_withdrawal(db, withdrawal_id)
#     if db_withdrawal:
#         update_data = withdrawal.dict(exclude_unset=True)
#         for key, value in update_data.items():
#             setattr(db_withdrawal, key, value)
#         db.commit()
#         db.refresh(db_withdrawal)
#     return db_withdrawal

def delete_withdrawal(db: Session, withdrawal_id: int) -> bool:
    db_withdrawal = get_withdrawal(db, withdrawal_id)
    if db_withdrawal:
        db.delete(db_withdrawal)
        db.commit()
        return True
    return False


def withdrawal_status_update(db: Session, withdrawal_id: int, withdrawal: WithdrawalUpdate) -> Optional[Withdrawal]:
    db_withdrawal = get_withdrawal(db, withdrawal_id)
    if not db_withdrawal:
        return None

    # Only proceed if approving the withdrawal
    if withdrawal.status.lower() == "approved":
        if db_withdrawal.status.lower() == "approved":
            # Already approved
            return db_withdrawal

        db_wallet = db.query(Wallet).filter(Wallet.user_id == db_withdrawal.user_id).first()
        if not db_wallet:
            raise HTTPException(status_code=404, detail="Wallet not found")

        amount_to_deduct = db_withdrawal.amount

        # 1) Deduct from current_profit
        if db_wallet.current_profit >= amount_to_deduct:
            db_wallet.current_profit -= amount_to_deduct
            amount_to_deduct = 0
        else:
            amount_to_deduct -= db_wallet.current_profit
            db_wallet.current_profit = 0

        # 2) Deduct from current_referral_bonus
        if amount_to_deduct > 0:
            if db_wallet.current_referral_bonus >= amount_to_deduct:
                db_wallet.current_referral_bonus -= amount_to_deduct
                amount_to_deduct = 0
            else:
                amount_to_deduct -= db_wallet.current_referral_bonus
                db_wallet.current_referral_bonus = 0

        # 3) Deduct from eligible_amount AND current_deposits (both reduced)
        if amount_to_deduct > 0:
            if db_wallet.eligible_amount >= amount_to_deduct and db_wallet.current_deposits >= amount_to_deduct:
                db_wallet.eligible_amount -= amount_to_deduct
                db_wallet.current_deposits -= amount_to_deduct
                amount_to_deduct = 0
            else:
                # Insufficient deposit funds (should not happen if validated earlier)
                raise HTTPException(status_code=400, detail="Insufficient deposit funds in wallet for approval")

        # Deduct total withdrawal from balance
        db_wallet.balance -= db_withdrawal.amount

        # Update withdrawal status and approval timestamp
        db_withdrawal.status = withdrawal.status
        db_withdrawal.approved_at = func.now()

        transaction_ledger = db.query(TransactionLedger).filter(TransactionLedger.user_id == db_withdrawal.user_id, TransactionLedger.created_at == db_withdrawal.created_at).first()
        if transaction_ledger:
            transaction_ledger.description = "Approved"
            db.commit()
            db.refresh(transaction_ledger)

        db.commit()
        db.refresh(db_wallet)
        db.refresh(db_withdrawal)

        return db_withdrawal

    else:
        # For other statuses (rejected, cancelled, etc), just update the status
        db_withdrawal.status = withdrawal.status
        
        transaction_ledger = db.query(TransactionLedger).filter(TransactionLedger.user_id == db_withdrawal.user_id).first()
        if transaction_ledger:
            transaction_ledger.description = db_withdrawal.status
            db.commit()
            db.refresh(transaction_ledger)
        db.commit()
        db.refresh(db_withdrawal)
        return db_withdrawal
    
    
def get_withdrawal_summary(db: Session, user_id: int) -> Optional[Withdrawal]:
    
    wallet = db.query(Wallet).filter(Wallet.user_id == user_id).first()
    
    current_balance = wallet.balance if wallet.balance else 0
    
    available_for_withdrawal = ((wallet.current_profit or 0.0) + (wallet.current_referral_bonus or 0.0) + (wallet.eligible_amount or 0.0))
    
    pending_withdrawal_sum = (db.query(func.coalesce(func.sum(Withdrawal.amount), 0)).filter(Withdrawal.user_id == user_id, Withdrawal.status == "pending").scalar())

    total_withdrawn_amound = (db.query(func.coalesce(func.sum(Withdrawal.amount), 0)).filter(Withdrawal.user_id == user_id, Withdrawal.status == "approved").scalar())
    
    withdrawal_updated_date = db.query(Withdrawal).filter(Withdrawal.user_id == user_id).order_by(Withdrawal.updated_at.desc()).first()
    
    wallet_updated_date = db.query(Wallet).filter(Wallet.user_id == user_id).order_by(Wallet.updated_at.desc()).first()
    
    withdrawal_time = withdrawal_updated_date.updated_at if withdrawal_updated_date else None
    wallet_time = wallet_updated_date.updated_at if wallet_updated_date else None
    
    return {
        "current_balance": current_balance,
        "available_for_withdrawal": available_for_withdrawal,
        "pending_withdrawal_sum": pending_withdrawal_sum,
        "total_withdrawn_amound": total_withdrawn_amound,
        "last_updated_date": max(filter(None, [withdrawal_time, wallet_time]))
    }
