from datetime import datetime
from typing import Optional
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey



# WALLETS Table
class Wallet(BaseModel):
    __tablename__ = "wallets"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    balance = Column(Float, default=0.0)
    total_deposits = Column(Float, default=0.0)
    eligible_amount = Column(Float, default=0.0) 
    total_profit = Column(Float, default=0.0)
    total_referral_bonus = Column(Float, default=0.0)
    current_deposits = Column(Float, default=0.0)
    current_profit = Column(Float, default=0.0)
    current_referral_bonus = Column(Float, default=0.0)
    
    user = relationship("User", back_populates="wallet")
    
    
    @property
    def user_name(self):
        return self.user.name if self.user else None
    
    @property
    def user_balance(self) -> float:
        return (self.current_profit or 0.0) + (self.current_referral_bonus or 0.0) + (self.eligible_amount or 0.0)
