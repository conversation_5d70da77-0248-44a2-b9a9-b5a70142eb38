from sqlalchemy.orm import Session
from app.models.deposite_slab import DepositSlab
from app.schemas.deposite_slab import DepositSlabCreate, DepositSlabUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page

def get_deposit_slabs(db: Session, params: PaginationParams) -> Page[DepositSlab]:
    query = db.query(DepositSlab).order_by(DepositSlab.id.desc()).filter(DepositSlab.is_deleted == False)
    return paginate(query, params)

def get_deposit_slab(db: Session, DepositSlab_id: int) -> Optional[DepositSlab]:
    return db.query(DepositSlab).filter(DepositSlab.id == DepositSlab_id).first()

def create_deposit_slab(db: Session, DepositSlabData: DepositSlabCreate) -> DepositSlab:
    db_DepositSlab = DepositSlab(**DepositSlabData.dict())
    db.add(db_DepositSlab)
    db.commit()
    db.refresh(db_DepositSlab)
    return db_DepositSlab

def update_deposit_slab(db: Session, DepositSlab_id: int, DepositSlab: DepositSlabUpdate) -> Optional[DepositSlab]:
    db_DepositSlab = get_deposit_slab(db, DepositSlab_id)
    if db_DepositSlab:
        update_data = DepositSlab.dict(exclude_none=True)
        for key, value in update_data.items():
            setattr(db_DepositSlab, key, value)
        db.commit()
        db.refresh(db_DepositSlab)
    return db_DepositSlab

def delete_deposit_slab(db: Session, DepositSlab_id: int) -> bool:
    db_DepositSlab = get_deposit_slab(db, DepositSlab_id)
    if db_DepositSlab:
        db_DepositSlab.is_deleted = True
        db.commit()
        db.refresh(db_DepositSlab)
        return True
    return False