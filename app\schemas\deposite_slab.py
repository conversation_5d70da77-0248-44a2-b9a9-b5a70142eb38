from datetime import datetime
from typing import Optional
from pydantic import BaseModel as PydanticBaseModel
from app.schemas.base import CustomBaseModel

class DepositSlabBase(CustomBaseModel):
    name: str
    amount: float
    image: Optional[str] = None
    is_active: bool = True


class DepositSlabCreate(DepositSlabBase):
    pass


class DepositSlabUpdate(PydanticBaseModel):
    name: Optional[str] = None
    amount: Optional[float] = None
    image: Optional[str] = None
    is_active: Optional[bool] = None


class DepositSlabOut(DepositSlabBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True
