from sqlalchemy.orm import Session
from app.models.deposits import Deposit
from app.schemas.deposits import DepositCreate, DepositUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, Page
from app.models.wallets import Wallet
from datetime import datetime
from app.services.referral_service import admin_approve_deposit_and_process_commissions
from app.models.users import User
from app.models.transaction_logs import TransactionLedger, TransactionType
from sqlalchemy.orm import joinedload
from app.tasks import process_deposit_reward_updates
from fastapi import HTTPException


def get_deposits(
    db: Session,
    params: PaginationParams,
    status: Optional[str] = None,
    search: Optional[str] = None
) -> Page[Deposit]:
    query = (
        db.query(Deposit)
        .options(
            joinedload(Deposit.user),
            joinedload(Deposit.reviewed_by_admin)
        )
        .order_by(Deposit.id.desc())
    )

    if status:
        query = query.filter(Deposit.status == status)

    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (Deposit.transaction_id.ilike(search_term)) |
            (Deposit.user.has(User.name.ilike(search_term)))  # Join required for user's name
        )

    return paginate(query, params)



def get_user_deposits(db: Session, params: PaginationParams, user_id: int) -> Page[Deposit]:
    query = db.query(Deposit).options(joinedload(Deposit.user),joinedload(Deposit.reviewed_by_admin)).filter(Deposit.user_id == user_id).order_by(Deposit.id.desc())
    return paginate(query, params)

def get_deposit(db: Session, deposit_id: int) -> Optional[Deposit]:
    return db.query(Deposit).filter(Deposit.id == deposit_id).first()

def create_deposit(db: Session, Deposit_data: DepositCreate, user_id: int) -> Deposit:
    
    existing_deposit = db.query(Deposit).filter(Deposit.transaction_id == Deposit_data.transaction_id).first()
    
    if existing_deposit:
        raise HTTPException(status_code=400, detail=f"Transaction with transaction ID {Deposit_data.transaction_id} already exists.")
    db_deposit = Deposit(user_id=user_id, **Deposit_data.dict(exclude={"user_id"}))
    db.add(db_deposit)
    
    new_transaction_log = TransactionLedger(
    user_id=user_id,
    amount=Deposit_data.amount,
    transaction_type="deposit_wallet_credit",
    reference_id=Deposit_data.transaction_id, # Will be available after flush/commit if not already
    description="Pending"
    # created_at is handled by BaseModel or server_default
    )
    db.add(new_transaction_log)
    db.commit()
    db.refresh(new_transaction_log)
    db.refresh(db_deposit)
    return db_deposit

def approve_deposit(db: Session, deposit_id: int, status: DepositUpdate, current_user_id: int) -> Optional[Deposit]:
    db_deposit = get_deposit(db, deposit_id)
    # if db_deposit:
        # update_data = Deposit.dict(exclude_unset=True)
        # for key, value in update_data.items():
        #     setattr(db_deposit, key, value)
        # db_deposit.status = deposit.status
        # db_deposit.reviewed_by_admin_id = current_user_id
        # db_deposit.approved_at = datetime.utcnow()
    # print("update deposit about to call admin_approve_deposit_and_process_commissions")
    admin_approve = admin_approve_deposit_and_process_commissions(deposit_id=db_deposit.id, deposit_status=status, db=db, admin_user_id=current_user_id)
    # print("update deposit admin_approve_deposit_and_process_commissions returned and before commit", admin_approve)
    db.commit()
    db.refresh(db_deposit)
    # print("this is deposit status", db_deposit.status)
    if db_deposit.status == "approved":
        # Update the user's wallet balance if the deposit is approved
        user_wallet = db.query(Wallet).filter(Wallet.user_id == db_deposit.user_id).first()
        if user_wallet:
            user_wallet.balance = (user_wallet.balance or 0.0) + db_deposit.amount
            user_wallet.total_deposits = (user_wallet.total_deposits or 0.0) + db_deposit.amount
            user_wallet.current_deposits = (user_wallet.current_deposits or 0.0) + db_deposit.amount
            db.commit()
            db.refresh(user_wallet)
        print("deposit approved and about to call process_deposit_reward_updates")
        process_deposit_reward_updates.delay(db_deposit.user_id, db_deposit.amount)
        print("process_deposit_reward_updates called")
    
    transaction_ledger = db.query(TransactionLedger).filter(TransactionLedger.user_id == db_deposit.user_id, TransactionLedger.reference_id == db_deposit.transaction_id).first()
    if transaction_ledger:
        transaction_ledger.description = db_deposit.status
        db.commit()
        db.refresh(transaction_ledger)
    
    return db_deposit

def delete_deposit(db: Session, deposit_id: int) -> bool:
    db_deposit = get_deposit(db, deposit_id)
    if db_deposit:
        db.delete(db_deposit)
        db.commit()
        return True
    return False