from sqlalchemy.orm import Session
from app.models.terms_and_conditions import TermsAndConditions
from app.schemas.terms_and_conditions import TermsAndConditionsCreate, TermsAndConditionsUpdate
from typing import List, Optional
from app.utils.pagination import paginate, PaginationParams, <PERSON>

def get_terms_and_conditions(db: Session, params: PaginationParams) -> Page[TermsAndConditions]:
    query = db.query(TermsAndConditions)
    return paginate(query, params)

def get_terms_and_condition(db: Session, terms_and_condition_id: int) -> Optional[TermsAndConditions]:
    return db.query(TermsAndConditions).filter(TermsAndConditions.id == terms_and_condition_id).first()

def create_terms_and_condition(db: Session, terms_and_condition: TermsAndConditionsCreate) -> TermsAndConditions:
    db_terms_and_condition = TermsAndConditions(**terms_and_condition.dict())
    db.add(db_terms_and_condition)
    db.commit()
    db.refresh(db_terms_and_condition)
    return db_terms_and_condition

def update_terms_and_condition(db: Session, terms_and_condition_id: int, terms_and_condition: TermsAndConditionsUpdate) -> Optional[TermsAndConditions]:
    db_terms_and_condition = get_terms_and_condition(db, terms_and_condition_id)
    if db_terms_and_condition:
        update_data = terms_and_condition.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_terms_and_condition, key, value)
        db.commit()
        db.refresh(db_terms_and_condition)
    return db_terms_and_condition

def delete_terms_and_condition(db: Session, terms_and_condition_id: int) -> bool:
    db_terms_and_condition = get_terms_and_condition(db, terms_and_condition_id)
    if db_terms_and_condition:
        db.delete(db_terms_and_condition)
        db.commit()
        return True
    return False