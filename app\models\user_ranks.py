from datetime import datetime
from typing import Optional, List
from sqlmodel import Field, SQLModel, Relationship
from app.models.base import BaseModel
from sqlalchemy.orm import relationship
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text, Enum as sqlEnum, Date





# class Rank(BaseModel):
#     __tablename__ = "ranks"

#     id = Column(Integer, primary_key=True)
#     name = Column(String(50), unique=True, nullable=False)  # bronze, silver...
#     discription = Column(Text, nullable=True)
#     threshold_amount = Column(Float, nullable=False)  # e.g., 25000, 50000, ...
#     bonus_amount = Column(Float, nullable=False)  # e.g., 4000, 8000...
#     tier_level = Column(String(100), nullable=False)  # 1, 2, 3, 4, 5...

#     user = relationship("User", back_populates="rank")



class Rank(BaseModel):
    __tablename__ = "ranks"

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)  # bronze, silver...
    discription = Column(Text, nullable=True)
    threshold_amount = Column(Float, nullable=False)  # e.g., 25000, 50000, ...
    bonus_amount = Column(Float, nullable=False)  # e.g., 4000, 8000...
    tier_level = Column(String(100), nullable=False)  # 1, 2, 3, 4, 5...
    
    # Milestone reward fields
    required_direct_users = Column(Integer, nullable=True)
    required_total_users = Column(Integer, nullable=True)
    required_deposit = Column(Float, nullable=True)
    reward_description = Column(String(500), nullable=True)

    user = relationship("User", back_populates="rank")
    reward_records = relationship("UserRewardProgress", back_populates="rank")