from fastapi import Form, File, UploadFile, Depends, status, APIRouter, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.schemas.users import UserCreate, UserUpdate, UserOut, Login, KYCStatusUpdate
from app.services.users_service import get_users, get_user, get_token_user, create_user, update_user, delete_user, get_user_referrals, update_kyc_status, send_otp_to_email, request_verification
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from fastapi.security import OAuth2PasswordRequestForm
from app.models.users import User
from app.utils.auth_utility import create_access_token, verify_password, create_refresh_token, decode_access_token
from datetime import date
from typing import Optional
from app.models.users import StatusEnum
from app.utils.save_image_to_local import save_image
from fastapi import Query
import random
from datetime import datetime, timedelta
from app.models.users import EmailOTP
from app.utils.auth_utility import get_current_user
from app.utils.auth_utility import get_user_by_mail, hash_password
from app.utils.pagination import paginate, PaginationParams, Page

router = APIRouter(
    prefix="/users",
    tags=["users"]
)

@router.post("/login")
def login(login_data: Login, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == login_data.email).first()
    if not user or not verify_password(login_data.password, user.password):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Invalid credentials")

    access_token = create_access_token(data={"sub": user.email})
    refresh_token = create_refresh_token(data={"sub": user.email})
    user_role = "admin" if user.is_admin else "user"

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "user_role": user_role,
        "token_type": "bearer"
    }


@router.post("/refresh")
def refresh_token(refresh_token: str):
    payload = decode_access_token(refresh_token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token")

    # Optionally validate against stored refresh token in DB (recommended in production)
    new_access_token = create_access_token(data={"sub": payload["sub"]})
    return {"access_token": new_access_token, "token_type": "bearer"}



@router.get("/", response_model=Page[UserOut])
def read_users(
    status: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
):
    return get_users(db, params=params, status=status, search=search)

@router.get("/{user_id}", response_model=UserOut)
def read_user(user_id: int, db: Session = Depends(get_db)):
    user = get_user(db=db, User_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return user


@router.get("/profile/user", response_model=UserOut)
def read_token_user(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    user = get_token_user(db=db, User_id=current_user.id)
    if user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return user

@router.post("/", response_model=UserOut, status_code=status.HTTP_201_CREATED)
def create_new_user(name: str = Form(...),
                    dob: Optional[date] = Form(None),
                    nationality: str = Form(...),
                    country_of_residence: str = Form(...),
                    preferred_currency: Optional[str] = Form(None),
                    address: str = Form(...),
                    country_code: str = Form(...),
                    phone: str = Form(...),
                    email: str = Form(...),
                    national_id: UploadFile = File(None),
                    passport: UploadFile = File(None),
                    is_admin: bool = Form(False),
                    is_active: bool = Form(True),
                    is_kyc_verified: bool = Form(False),
                    kyc_status: StatusEnum = Form(StatusEnum.pending),
                    referral_user_code: Optional[str] = Form(None),
                    password: str = Form(...),
                    profile_picture: Optional[UploadFile] = File(None),
                    db: Session = Depends(get_db)):
    
    image_path = save_image(profile_picture) if profile_picture else None
    national_id_path = save_image(national_id) if national_id else None
    passport_path = save_image(passport) if passport else None
    user_data = UserCreate(
        name=name,
        dob=dob,
        nationality=nationality,
        country_of_residence=country_of_residence,
        preferred_currency=preferred_currency,
        address=address,
        country_code=country_code,
        phone=phone,
        email=email,
        national_id=national_id_path,
        passport=passport_path,
        rank_id=1,
        is_admin=is_admin,
        is_active=is_active,
        is_kyc_verified=is_kyc_verified,
        kyc_status=kyc_status,
        referral_user_code=referral_user_code,
        password=password,
        profile_picture=image_path
    )
    return create_user(db=db, user=user_data)

@router.put("/{user_id}", response_model=UserOut)
def update_existing_user(
    user_id: int,
    name: Optional[str] = Form(None),
    nationality: Optional[str] = Form(None),
    country_of_residence: Optional[str] = Form(None),
    preferred_currency: Optional[int] = Form(None),
    address: Optional[str] = Form(None),
    country_code: Optional[str] = Form(None),
    phone: Optional[str] = Form(None),
    email: Optional[str] = Form(None),
    password: Optional[str] = Form(None),
    profile_picture: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    
    if password:
        hashed_password = hash_password(password)

    # Build a dictionary of the non-None form fields
    image_path = save_image(profile_picture) if profile_picture else None
    user_data = UserUpdate(
        name=name,
        nationality=nationality,
        country_of_residence=country_of_residence,
        preferred_currency=preferred_currency,
        address=address,
        country_code=country_code,
        phone=phone,
        email=email,
        password=hashed_password,
        profile_picture=image_path
    )

    updated_user = update_user(db=db, User_id=user_id, User=user_data)
    if updated_user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return updated_user

@router.delete("/{user_id}", status_code=status.HTTP_200_OK)
def delete_existing_user(user_id: int, db: Session = Depends(get_db)):
    success = delete_user(db=db, User_id=user_id)
    if not success:
        raise HTTPException(status_code=404, detail="user not found")
    return {"detail": "user deleted successfully"}


@router.get("/{user_id}/referrals", response_model=List[dict])
def read_user_referrals(user_id: int, db: Session = Depends(get_db)):
    user = get_user_referrals(db=db, user_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return user


@router.post("/{user_id}/kyc_status", response_model=UserOut)
def update_user_kyc_status(user_id: int, kyc_status: KYCStatusUpdate, db: Session = Depends(get_db)):
    updated_user = update_kyc_status(db=db, user_id=user_id, kyc_status=kyc_status)
    if updated_user is None:
        raise HTTPException(status_code=404, detail="user not found")
    return updated_user

@router.post("/request/verify_kyc")
def request_kyc_verify(national_id: UploadFile = File(None),
                       passport: UploadFile = File(None),
                       db: Session = Depends(get_db),
                       current_user=Depends(get_current_user)):
    
    request = request_verification(db=db, user_id=current_user.id, national_id=national_id, passport=passport)
    if request is None:
        raise HTTPException(status_code=400, detail="request failed")
    return {"detail": "request sent successfully"}


@router.post("/email_verification")
def verify_email(email: str = Form(...), db: Session = Depends(get_db)):
    # otp = random.randint(100000, 999999)
    otp = "123456"
    expires_at = datetime.utcnow() + timedelta(minutes=5)

    # Store OTP in DB
    existing = db.query(EmailOTP).filter(EmailOTP.email == email).first()
    if existing:
        existing.otp = otp
        existing.expires_at = expires_at
    else:
        db.add(EmailOTP(email=email, otp=otp, expires_at=expires_at))

    db.commit()
    
    # send_otp_to_email(email, otp)

    return {"message": "OTP sent to email"}
    # Implement your email verification logic here


@router.post("/verify/otp")
def check_otp(mail: str = Form(...), otp: int = Form(...), db: Session = Depends(get_db)):
    record = db.query(EmailOTP).filter(EmailOTP.email == mail).first()
    
    if not record:
        raise HTTPException(status_code=400, detail="No OTP found for this email")
    
    if record.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="OTP expired")

    if record.otp != otp:
        raise HTTPException(status_code=400, detail="Invalid OTP")
    
    if record.otp == otp:
        db.delete(record)
        db.commit()

    return {"message": "Email verified successfully"}

    
@router.post("/password/otp")
def check_otp(mail: str = Form(...), otp: int = Form(...), db: Session = Depends(get_db)):
    record = db.query(EmailOTP).filter(EmailOTP.email == mail).first()
    user = get_user_by_mail(db, mail)
    access_token = create_access_token(data={"sub": user.email})
    
    if not record:
        raise HTTPException(status_code=400, detail="No OTP found for this email")
    
    if record.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="OTP expired")

    if record.otp != otp:
        raise HTTPException(status_code=400, detail="Invalid OTP")
    
    if record.otp == otp:
        db.delete(record)
        db.commit()

    return {"message": "Email verified successfully", "user_id": user.id, "access_token": access_token}



@router.get("/direct/user/referrals", response_model=Page[UserOut])
def get_direct_referrals(params: PaginationParams = Depends(), db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    direct_referrals = db.query(User).filter(User.referrer_id == current_user.id)
    return paginate(direct_referrals, params)
