from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import func
from sqlalchemy.orm import Session
from typing import List
from app.schemas.withdrawals import WithdrawalCreate, WithdrawalUpdate, WithdrawalOut
from app.services.withdrawal_service import get_withdrawals, get_user_withdrawals, get_withdrawal, create_withdrawal, delete_withdrawal, withdrawal_status_update, get_withdrawal_summary
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user, is_admin_user
from app.models.withdrawals import Withdrawal
from fastapi import Query
from typing import Optional


router = APIRouter(
    prefix="/withdrawals",
    tags=["withdrawals"]
)

@router.get("/", response_model=Page[WithdrawalOut])
def read_withdrawals(
    status: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_withdrawals(db, params=params, status=status, search=search)


@router.get("/user", response_model=Page[WithdrawalOut])
def read_withdrawals(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_user_withdrawals(db, params=params, user_id=current_user.id)

@router.get("/{withdrawal_id}", response_model=WithdrawalOut)
def read_withdrawal(withdrawal_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    withdrawal = get_withdrawal(db, withdrawal_id=withdrawal_id)
    if withdrawal is None:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return withdrawal

@router.post("/", status_code=status.HTTP_201_CREATED)
def create_new_withdrawal(withdrawal: WithdrawalCreate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    return create_withdrawal(db=db, withdrawal=withdrawal, user_id=current_user.id)

# @router.put("/{withdrawal_id}", response_model=WithdrawalOut)
# def update_existing_withdrawal(withdrawal_id: int, withdrawal: WithdrawalUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
#     updated_withdrawal = update_withdrawal(db=db, withdrawal_id=withdrawal_id, withdrawal=withdrawal)
#     if updated_withdrawal is None:
#         raise HTTPException(status_code=404, detail="withdrawal not found")
#     return updated_withdrawal

@router.delete("/{withdrawal_id}", status_code=status.HTTP_200_OK)
def delete_existing_withdrawal(withdrawal_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_withdrawal(db=db, withdrawal_id=withdrawal_id)
    if not success:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return {"detail": "withdrawal deleted successfully"}


@router.post("/{withdrawal_id}/withdrawal_status", response_model=WithdrawalOut)
def update_withdrawal_status(withdrawal_id: int, withdrawal: WithdrawalUpdate, db: Session = Depends(get_db), current_user=Depends(is_admin_user)):
    updated_withdrawal = withdrawal_status_update(db=db, withdrawal_id=withdrawal_id, withdrawal=withdrawal)
    if updated_withdrawal is None:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return updated_withdrawal


@router.get("/user/summary", response_model=dict)
def withdrawal_summary(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    withdrawal = get_withdrawal_summary(db, user_id=current_user.id)
    if withdrawal is None:
        raise HTTPException(status_code=404, detail="withdrawal not found")
    return withdrawal


@router.get("/dashboard/header")
def withdrawal_header(db: Session = Depends(get_db), current_user=Depends(is_admin_user)):
    total_withdrawal_count = db.query(func.count(Withdrawal.id)).scalar() or 0
    total_pending_count = db.query(func.count(Withdrawal.id)).filter(Withdrawal.status == "pending").scalar() or 0
    total_approved_count = db.query(func.count(Withdrawal.id)).filter(Withdrawal.status == "approved").scalar() or 0
    total_rejected_count = db.query(func.count(Withdrawal.id)).filter(Withdrawal.status == "rejected").scalar() or 0

    return {
        "total_withdrawals": total_withdrawal_count,
        "pending_withdrawals": total_pending_count,
        "approved_withdrawals": total_approved_count,
        "rejected_withdrawals": total_rejected_count,
    }