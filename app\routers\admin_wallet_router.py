from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List
from app.schemas.admin_wallet import AdminWalletCreate, AdminWalletUpdate, AdminWalletOut
from app.services.admin_wallet_service import get_admin_wallets, get_admin_wallet, create_admin_wallet, update_admin_wallet, delete_admin_wallet
from app.config.db import get_db
from app.utils.pagination import PaginationParams, Page
from app.utils.auth_utility import get_current_user, is_admin_user
from app.utils.save_image_to_local import save_image


router = APIRouter(
    prefix="/admin_wallets",
    tags=["admin_wallets"]
)

@router.get("/", response_model=Page[AdminWalletOut])
def read_admin_wallets(
    params: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    return get_admin_wallets(db, params=params)

@router.get("/user", response_model=AdminWalletOut)
def read_admin_wallet(db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    admin_wallet = get_admin_wallet(db)
    if admin_wallet is None:
        raise HTTPException(status_code=404, detail="admin_wallet not found")
    return admin_wallet

@router.post("/", response_model=AdminWalletOut, status_code=status.HTTP_201_CREATED)
def create_new_admin_wallet(wallet_address: str = Form(...),
                            qr_code: UploadFile = File(...),
                            network: str = Form(...),
                            db: Session = Depends(get_db),
                            current_user=Depends(is_admin_user)):
    
    qr_code_path = save_image(qr_code) if qr_code else None

    admin_wallet_data = AdminWalletCreate(
        wallet_address=wallet_address,
        qr_code=qr_code_path,
        network=network
    )
    
    return create_admin_wallet(db=db, AdminWalletData=admin_wallet_data)

@router.put("/{admin_wallet_id}", response_model=AdminWalletOut)
def update_existing_admin_wallet(admin_wallet_id: int, admin_wallet: AdminWalletUpdate, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    updated_admin_wallet = update_admin_wallet(db=db, admin_wallet_id=admin_wallet_id, admin_wallet=admin_wallet)
    if updated_admin_wallet is None:
        raise HTTPException(status_code=404, detail="admin_wallet not found")
    return updated_admin_wallet

@router.delete("/{admin_wallet_id}", status_code=status.HTTP_200_OK)
def delete_existing_admin_wallet(admin_wallet_id: int, db: Session = Depends(get_db), current_user=Depends(get_current_user)):
    success = delete_admin_wallet(db=db, admin_wallet_id=admin_wallet_id)
    if not success:
        raise HTTPException(status_code=404, detail="admin_wallet not found")
    return {"detail": "admin_wallet deleted successfully"}